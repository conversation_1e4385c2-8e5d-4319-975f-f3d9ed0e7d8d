const express = require('express');
const { verifyToken, requireRole } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const pool = require('../db');
const axios = require('axios');

const router = express.Router();

// 从 Keycloak 获取用户列表的函数
const getKeycloakUsers = async () => {
  try {
    console.log('尝试从Keycloak获取用户列表...');

    // 启用Keycloak用户获取功能
    // 方式1：尝试使用master realm的admin-cli客户端
    const tokenResponse = await axios.post(
      `${process.env.KEYCLOAK_URL}/realms/master/protocol/openid-connect/token`,
      new URLSearchParams({
        grant_type: 'password',
        client_id: 'admin-cli',
        username: process.env.KEYCLOAK_ADMIN_USERNAME || 'admin',
        password: process.env.KEYCLOAK_ADMIN_PASSWORD || 'admin'
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    const adminToken = tokenResponse.data.access_token;
    console.log('✅ 成功获取管理员token');

    // 获取用户列表
    const usersResponse = await axios.get(
      `${process.env.KEYCLOAK_URL}/admin/realms/${process.env.KEYCLOAK_REALM}/users`,
      {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log(`✅ 从Keycloak获取到 ${usersResponse.data.length} 个用户`);
    return usersResponse.data;
  } catch (error) {
    console.error('从Keycloak获取用户失败:', error.response?.data || error.message);
    // 如果无法从Keycloak获取用户，返回空数组，这样至少可以显示数据库中的用户
    return [];
  }
};

// 角色映射函数
const mapRole = (sysAccess) => {
  const roleMap = {
    'admin': 'Administrator',
    'owner': 'Administrator',
    'guest': 'Developer'
  };
  return roleMap[sysAccess] || 'Developer';
};

// 状态映射函数
const mapStatus = (enabled) => {
  return enabled ? 'Active' : 'Inactive';
};

// 格式化用户数据
const formatUserData = (user) => {
  const fullName = [user.first_name, user.last_name].filter(Boolean).join(' ') || user.username;
  return {
    id: user.id,
    name: fullName,
    username: user.username,
    email: user.email,
    role: mapRole(user.sys_access),
    status: mapStatus(user.enabled),
    dateJoined: user.created_at ? user.created_at.toISOString().split('T')[0] : '',
    phone: user.phone,
    keycloak_id: user.keycloak_id,
    sys_access: user.sys_access,
    enabled: user.enabled,
    first_name: user.first_name,
    last_name: user.last_name
  };
};

// 获取用户列表 - 管理员可以看到所有用户，普通用户只能看到自己
router.get('/users', verifyToken, asyncHandler(async (req, res) => {
  const {
    page = 1,
    pageSize = 10,
    search = '',
    role = '',
    status = '',
    sortField = 'created_at',
    sortOrder = 'desc'
  } = req.query;

  try {
    // 检查用户权限
    const isAdmin = req.user.roles.includes('admin') || req.user.roles.includes('owner');

    if (!isAdmin) {
      // 普通用户只能看到自己的信息
      const userQuery = `
        SELECT id, username, email, phone, sys_access, keycloak_id, enabled,
               first_name, last_name, created_at, updated_at
        FROM users
        WHERE keycloak_id = ? OR username = ?
      `;

      const userResult = await new Promise((resolve, reject) => {
        pool.query(userQuery, [req.user.id, req.user.username], (err, result) => {
          if (err) reject(err);
          else resolve(result);
        });
      });

      const formattedUsers = userResult.map(formatUserData);

      return res.json({
        success: true,
        data: {
          users: formattedUsers,
          pagination: {
            current: 1,
            pageSize: formattedUsers.length,
            total: formattedUsers.length,
            totalPages: 1
          }
        }
      });
    }
    // 管理员可以看到所有用户 - 从 Keycloak 获取用户数据
    // console.log('管理员获取用户列表');

    // 获取 Keycloak 用户数据
    const keycloakUsers = await getKeycloakUsers();
    // console.log(`从Keycloak获取到 ${keycloakUsers.length} 个用户`);

    // 获取数据库中的用户数据
    const dbQuery = `
      SELECT id, username, email, phone, sys_access, keycloak_id, enabled,
             first_name, last_name, created_at, updated_at
      FROM users
    `;

    const dbUsers = await new Promise((resolve, reject) => {
      pool.query(dbQuery, [], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    console.log(`从数据库获取到 ${dbUsers.length} 个用户`);

    // 合并 Keycloak 和数据库用户数据
    const mergedUsers = [];
    const dbUserMap = new Map();

    // 创建数据库用户映射
    dbUsers.forEach(user => {
      if (user.keycloak_id) {
        dbUserMap.set(user.keycloak_id, user);
      }
      if (user.username) {
        dbUserMap.set(user.username, user);
      }
    });

    // 合并用户数据
    keycloakUsers.forEach(kcUser => {
      const dbUser = dbUserMap.get(kcUser.id) || dbUserMap.get(kcUser.username);

      const mergedUser = {
        id: dbUser?.id || null,
        keycloak_id: kcUser.id,
        username: kcUser.username,
        email: kcUser.email,
        first_name: kcUser.firstName || '',
        last_name: kcUser.lastName || '',
        phone: dbUser?.phone || '',
        enabled: kcUser.enabled,
        created_at: dbUser?.created_at || new Date(kcUser.createdTimestamp),
        updated_at: dbUser?.updated_at || new Date(),
        sys_access: dbUser?.sys_access || 'guest'
      };

      mergedUsers.push(mergedUser);
    });

    // 添加只在数据库中存在的用户（没有keycloak_id的）
    dbUsers.forEach(dbUser => {
      if (!dbUser.keycloak_id) {
        mergedUsers.push(dbUser);
      }
    });

    // 应用搜索过滤
    let filteredUsers = mergedUsers;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = mergedUsers.filter(user =>
        (user.username && user.username.toLowerCase().includes(searchLower)) ||
        (user.email && user.email.toLowerCase().includes(searchLower)) ||
        (user.first_name && user.first_name.toLowerCase().includes(searchLower)) ||
        (user.last_name && user.last_name.toLowerCase().includes(searchLower))
      );
    }

    // 应用角色过滤
    if (role) {
      const sysAccessMap = {
        'Administrator': ['admin', 'owner'],
        'Developer': ['guest'],
        'Manager': ['guest']
      };
      if (sysAccessMap[role]) {
        filteredUsers = filteredUsers.filter(user =>
          sysAccessMap[role].includes(user.sys_access)
        );
      }
    }

    // 应用状态过滤
    if (status) {
      const isActive = status === 'Active';
      filteredUsers = filteredUsers.filter(user => user.enabled === isActive);
    }

    // 排序
    const sortFieldMap = {
      'dateJoined': 'created_at',
      'name': 'username',
      'role': 'sys_access',
      'status': 'enabled'
    };
    const actualSortField = sortFieldMap[sortField] || 'created_at';

    filteredUsers.sort((a, b) => {
      let aVal = a[actualSortField];
      let bVal = b[actualSortField];

      if (actualSortField === 'created_at') {
        aVal = new Date(aVal);
        bVal = new Date(bVal);
      }

      if (sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });

    // 分页
    const total = filteredUsers.length;
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const paginatedUsers = filteredUsers.slice(offset, offset + parseInt(pageSize));

    // 格式化用户数据
    const formattedUsers = paginatedUsers.map(formatUserData);

    res.json({
      success: true,
      data: {
        users: formattedUsers,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          totalPages: Math.ceil(total / parseInt(pageSize))
        }
      }
    });

  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
}));

// 获取单个用户信息 - 临时移除权限检查用于测试
router.get('/users/:id', verifyToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const query = `
      SELECT id, username, email, phone, sys_access, keycloak_id, enabled,
             first_name, last_name, created_at, updated_at
      FROM users
      WHERE id = ?
    `;

    const result = await new Promise((resolve, reject) => {
      pool.query(query, [id], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = formatUserData(result[0]);

    res.json({
      success: true,
      data: user
    });

  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户详情失败',
      error: error.message
    });
  }
}));

// 创建用户 - 临时移除权限检查用于测试
router.post('/users', verifyToken, asyncHandler(async (req, res) => {
  const { name, username, email, role, phone, password = 'defaultPassword123' } = req.body;

  // 简单验证
  if (!username || !email || !role) {
    return res.status(400).json({
      success: false,
      message: '请填写所有必填字段'
    });
  }

  try {
    // 检查用户名和邮箱是否已存在
    const checkQuery = 'SELECT id FROM users WHERE username = ? OR email = ?';
    const existingUsers = await new Promise((resolve, reject) => {
      pool.query(checkQuery, [username, email], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '用户名或邮箱已存在'
      });
    }

    // 角色映射
    const roleMap = {
      'Administrator': 'admin',
      'Developer': 'guest',
      'Manager': 'guest'
    };
    const sysAccess = roleMap[role] || 'guest';

    // 解析姓名
    const nameParts = (name || username).split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    // 密码加密
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash(password, 10);

    // 插入新用户
    const insertQuery = `
      INSERT INTO users (username, password, email, phone, sys_access, enabled, first_name, last_name)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await new Promise((resolve, reject) => {
      pool.query(insertQuery, [username, hashedPassword, email, phone, sysAccess, true, firstName, lastName], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    // 获取新创建的用户信息
    const newUserId = result.insertId;
    const getUserQuery = `
      SELECT id, username, email, phone, sys_access, keycloak_id, enabled,
             first_name, last_name, created_at, updated_at
      FROM users
      WHERE id = ?
    `;

    const newUserResult = await new Promise((resolve, reject) => {
      pool.query(getUserQuery, [newUserId], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    const newUser = formatUserData(newUserResult[0]);

    res.json({
      success: true,
      data: newUser,
      message: '用户创建成功'
    });

  } catch (error) {
    console.error('创建用户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
}));

// 更新用户信息
router.put('/users/:id', requireRole('admin'), asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { name, username, email, role, phone, enabled } = req.body;

  try {
    // 检查用户是否存在
    const checkQuery = 'SELECT id FROM users WHERE id = ?';
    const existingUser = await new Promise((resolve, reject) => {
      pool.query(checkQuery, [id], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    if (existingUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查用户名和邮箱是否被其他用户使用
    if (username || email) {
      const duplicateQuery = 'SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?';
      const duplicateUsers = await new Promise((resolve, reject) => {
        pool.query(duplicateQuery, [username || '', email || '', id], (err, result) => {
          if (err) reject(err);
          else resolve(result);
        });
      });

      if (duplicateUsers.length > 0) {
        return res.status(400).json({
          success: false,
          message: '用户名或邮箱已被其他用户使用'
        });
      }
    }

    // 构建更新字段
    const updateFields = [];
    const updateValues = [];

    if (username) {
      updateFields.push('username = ?');
      updateValues.push(username);
    }
    if (email) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    if (phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    if (role) {
      const roleMap = {
        'Administrator': 'admin',
        'Developer': 'guest',
        'Manager': 'guest'
      };
      const sysAccess = roleMap[role] || 'guest';
      updateFields.push('sys_access = ?');
      updateValues.push(sysAccess);
    }
    if (enabled !== undefined) {
      updateFields.push('enabled = ?');
      updateValues.push(enabled);
    }
    if (name) {
      const nameParts = name.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';
      updateFields.push('first_name = ?', 'last_name = ?');
      updateValues.push(firstName, lastName);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有提供要更新的字段'
      });
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(id);

    const updateQuery = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;

    await new Promise((resolve, reject) => {
      pool.query(updateQuery, updateValues, (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    res.json({
      success: true,
      message: '用户信息更新成功'
    });

  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '更新用户信息失败',
      error: error.message
    });
  }
}));

// 删除用户
router.delete('/users/:id', requireRole('admin'), asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    // 检查用户是否存在
    const checkQuery = 'SELECT id, username FROM users WHERE id = ?';
    const existingUser = await new Promise((resolve, reject) => {
      pool.query(checkQuery, [id], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    if (existingUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 删除用户（注意：这会级联删除相关的会话和日志）
    const deleteQuery = 'DELETE FROM users WHERE id = ?';
    await new Promise((resolve, reject) => {
      pool.query(deleteQuery, [id], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    res.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
}));

module.exports = router;
