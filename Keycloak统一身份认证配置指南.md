# Keycloak统一身份认证配置指南

## 🎯 项目目标

实现基于Keycloak的统一身份认证系统，支持多个业务系统的单点登录(SSO)和统一用户管理。

## 🏗️ 系统架构

### 核心组件
- **Keycloak服务器**: 认证中心
- **Web管理系统**: 主要业务系统
- **GitLab**: 代码管理系统
- **软件建模组件**: 建模工具
- **其他业务系统**: 未来扩展的系统

### 认证流程
1. 用户访问任意系统
2. 系统重定向到Keycloak登录页面
3. 用户在Keycloak完成认证
4. Keycloak返回访问令牌
5. 用户可以无缝访问其他已集成的系统

## 🔧 Keycloak配置

### 1. Realm配置
```json
{
  "realm": "dev_xh_key",
  "displayName": "芯合统一认证域",
  "enabled": true,
  "sslRequired": "external",
  "registrationAllowed": false,
  "loginWithEmailAllowed": true,
  "duplicateEmailsAllowed": false,
  "resetPasswordAllowed": true,
  "editUsernameAllowed": false,
  "bruteForceProtected": true
}
```

### 2. 客户端配置

#### Web应用客户端
```json
{
  "clientId": "web-app",
  "name": "Web管理系统",
  "protocol": "openid-connect",
  "publicClient": false,
  "standardFlowEnabled": true,
  "directAccessGrantsEnabled": false,
  "serviceAccountsEnabled": true,
  "redirectUris": [
    "http://localhost:8088/*",
    "https://your-domain.com/*"
  ],
  "webOrigins": [
    "http://localhost:8088",
    "https://your-domain.com"
  ]
}
```

#### GitLab客户端
```json
{
  "clientId": "gitlab",
  "name": "GitLab系统",
  "protocol": "openid-connect",
  "publicClient": false,
  "standardFlowEnabled": true,
  "redirectUris": [
    "https://gitlab.your-domain.com/users/auth/openid_connect/callback"
  ],
  "attributes": {
    "saml.assertion.signature": "false",
    "saml.force.post.binding": "false",
    "saml.multivalued.roles": "false"
  }
}
```

### 3. 角色配置

#### 系统级角色
- **owner**: 系统超级管理员
- **admin**: 管理员
- **guest**: 普通用户

#### 客户端级角色映射
```json
{
  "web-app": {
    "roles": ["admin", "owner", "guest"]
  },
  "gitlab": {
    "roles": ["owner", "maintainer", "developer", "reporter", "guest"]
  },
  "modeling-tool": {
    "roles": ["architect", "designer", "viewer"]
  }
}
```


## 🔗 系统集成

### 1. Web应用集成
- 使用keycloak-js库进行前端集成
- 后端使用keycloak-connect中间件
- 实现自动令牌刷新和会话管理

### 2. GitLab集成
```yaml
# GitLab配置 (gitlab.rb)
gitlab_rails['omniauth_providers'] = [
  {
    name: 'openid_connect',
    label: 'Keycloak',
    args: {
      name: 'openid_connect',
      scope: ['openid', 'profile', 'email'],
      response_type: 'code',
      issuer: 'https://keycloak.your-domain.com/realms/enterprise',
      client_auth_method: 'query',
      discovery: true,
      uid_field: 'preferred_username',
      client_options: {
        identifier: 'gitlab',
        secret: 'your-client-secret',
        redirect_uri: 'https://gitlab.your-domain.com/users/auth/openid_connect/callback'
      }
    }
  }
]
```

### 3. 软件建模组件集成
- 实现OIDC客户端
- 配置角色映射
- 支持细粒度权限控制

## 👥 用户管理策略

### 1. 用户生命周期
- **创建**: 通过管理界面或API创建用户
- **激活**: 邮件验证或管理员激活
- **更新**: 用户信息同步更新到所有系统
- **禁用**: 统一禁用所有系统访问权限
- **删除**: 从所有系统中清除用户数据

### 2. 角色权限矩阵
| Web系统 | GitLab |  建模工具  |         描述           |
|---------|--------|-----------|------------------------|
|  owner  |  owner | architect | 系统管理员，拥有所有权限 |
|  admin  |  任意  |   任意     | 管理员，可以使用vip组件 |
|  guest  | guest  |   viewer  |     查看者，只读权限     |

### 3. 权限继承规则
- 高级角色自动包含低级角色权限
- 支持临时权限提升
- 基于项目的动态权限分配

## 🔒 安全配置

### 1. 密码策略
```json
{
  "passwordPolicy": "length(8) and digits(1) and lowerCase(1) and upperCase(1) and specialChars(1) and notUsername and notEmail and passwordHistory(3) and forceExpiredPasswordChange(365)"
}
```

### 2. 会话管理
- SSO会话超时: 8小时
- 刷新令牌有效期: 30天
- 记住我功能: 30天
- 并发会话限制: 3个

### 3. 安全事件监控
- 登录失败监控
- 异常访问检测
- 权限变更审计
- 会话异常监控

## 📊 监控和维护

### 1. 关键指标
- 用户登录成功率
- 系统响应时间
- 令牌刷新频率
- 权限变更频率

### 2. 日志管理
- 认证日志
- 授权日志
- 管理操作日志
- 错误日志

### 3. 备份策略
- 数据库定期备份
- 配置文件版本控制
- 证书和密钥管理
- 灾难恢复计划

## 🚀 实施步骤

### 阶段1: 基础设施搭建
1. 部署Keycloak服务器
2. 配置基础Realm和客户端
3. 设置基础角色和权限

### 阶段2: Web系统集成
1. 完善用户注册功能
2. 实现SSO登录
3. 集成用户管理界面

### 阶段3: GitLab集成
1. 配置GitLab OIDC
2. 实现用户同步
3. 测试权限映射

### 阶段4: 其他系统集成
1. 集成软件建模组件
2. 扩展到其他业务系统
3. 完善监控和维护

## 📝 注意事项

1. **数据一致性**: 确保用户信息在各系统间保持同步
2. **性能优化**: 合理配置缓存和连接池
3. **安全加固**: 定期更新和安全扫描
4. **用户体验**: 优化登录流程和错误提示
5. **文档维护**: 保持配置文档的及时更新
