import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Form, Input, Button, Upload, message, Typography, Card, Select, Checkbox, Switch, Divider, Tooltip, Radio } from 'antd';
import { UploadOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import { useLocation } from '@umijs/max';
import RepoBreadcrumb from '@/components/RepoBreadcrumb';
import { getProject, editProject } from '@/services/ant-design-pro/gitlab';

const { Title, Paragraph } = Typography;
const { TextArea } = Input;

// 扩展 location.state 的类型
interface LocationState {
  proId?: string | number;
  proName?: string;
  [key: string]: any;
}

const { Option } = Select;

const ProjectSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [collapsed, setCollapsed] = useState(false);
  const [branchCollapsed, setBranchCollapsed] = useState(false);
  const [mirrorCollapsed, setMirrorCollapsed] = useState(false);
  const [protectedCollapsed, setProtectedCollapsed] = useState(false);
  const [mergeRequestsCollapsed, setMergeRequestsCollapsed] = useState(false);
  const [visibilityCollapsed, setVisibilityCollapsed] = useState(false);
  const [autoCloseIssues, setAutoCloseIssues] = useState(false);
  const [mirrorPushChanges, setMirrorPushChanges] = useState(false);
  const [mirrorProtectedBranches, setMirrorProtectedBranches] = useState(false);

  // 合并请求设置
  const [mergeMethod, setMergeMethod] = useState('merge_commit');
  const [squashOption, setSquashOption] = useState('do_not_allow');

  // 可见性设置
  const [projectVisibility, setProjectVisibility] = useState('private');
  const [issuesEnabled, setIssuesEnabled] = useState(true);
  const [repositoryEnabled, setRepositoryEnabled] = useState(true);
  const [mergeRequestsEnabled, setMergeRequestsEnabled] = useState(false);
  const [forksEnabled, setForksEnabled] = useState(true);
  const [lfsEnabled, setLfsEnabled] = useState(true);
  const [packagesEnabled, setPackagesEnabled] = useState(false);
  const [cicdEnabled, setCicdEnabled] = useState(false);
  const [analyticsEnabled, setAnalyticsEnabled] = useState(false);
  const [securityEnabled, setSecurityEnabled] = useState(false);
  const [wikiEnabled, setWikiEnabled] = useState(false);
  const [snippetsEnabled, setSnippetsEnabled] = useState(false);
  const [operationsEnabled, setOperationsEnabled] = useState(false);
  // const [metricsEnabled, setMetricsEnabled] = useState(false); // 暂时不使用
  const [disableEmails, setDisableEmails] = useState(false);
  const [showEmoji, setShowEmoji] = useState(true);
  const [showUnicode, setShowUnicode] = useState(false);
  const location = useLocation();
  const state = location.state as LocationState;

  // 从URL获取项目信息，与files.tsx中获取方式相同
  const projectId = state?.proId || '2';
  const projectName = state?.proName || 'Workbench_v3';

  // 项目详细信息
  const [projectDetail, setProjectDetail] = useState<API.ListProjects | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // 获取项目详细信息
  useEffect(() => {
    const fetchProjectDetail = async () => {
      if (!projectId) return;

      setLoading(true);
      try {
        const response = await getProject({ id: Number(projectId) });
        if (response && response.length > 0) {
          const project = response[0];
          setProjectDetail(project);

          // 设置表单初始值
          form.setFieldsValue({
            projectName: project.name,
            projectId: project.id,
            description: project.description,
            // 设置项目可见性
            projectVisibility: project.visibility || 'private',
          });

          // 设置功能开关状态
          // 使用类型断言来避免类型错误
          const projectData = project as any;
          setIssuesEnabled(projectData.issues_enabled || false);
          setRepositoryEnabled(projectData.repository_enabled !== false); // 默认为true
          setMergeRequestsEnabled(projectData.merge_requests_enabled || false);
          setForksEnabled(projectData.forks_enabled || true);
          setLfsEnabled(projectData.lfs_enabled || true);
          setWikiEnabled(projectData.wiki_enabled || false);
          setSnippetsEnabled(projectData.snippets_enabled || false);
          setCicdEnabled(projectData.jobs_enabled || false);
          setPackagesEnabled(projectData.packages_enabled || false);
          setAnalyticsEnabled(projectData.analytics_enabled || false);
          setSecurityEnabled(projectData.security_enabled || false);
          setOperationsEnabled(projectData.operations_enabled || false);
        }
      } catch (error) {
        console.error('获取项目详情失败:', error);
        message.error('获取项目详情失败');
      } finally {
        setLoading(false);
      }
    };

    fetchProjectDetail();
  }, [projectId, form]);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    if (!projectId) {
      message.error('项目ID不存在');
      return;
    }

    try {
      // 准备提交的数据
      const submitData = {
        id: Number(projectId),
        name: values.projectName,
        description: values.description,
        visibility: projectVisibility,

        // 功能开关状态
        issues_enabled: issuesEnabled,
        repository_enabled: repositoryEnabled,
        merge_requests_enabled: mergeRequestsEnabled,
        forks_enabled: forksEnabled,
        lfs_enabled: lfsEnabled,
        wiki_enabled: wikiEnabled,
        snippets_enabled: snippetsEnabled,
        jobs_enabled: cicdEnabled,
        packages_enabled: packagesEnabled,
        analytics_enabled: analyticsEnabled,
        security_enabled: securityEnabled,
        operations_enabled: operationsEnabled,
      };

      // 调用编辑项目API
      await editProject(submitData as any);
      message.success('保存成功');
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 处理文件上传
  const handleFileChange = (info: any) => {
    let fileList = [...info.fileList];

    // 限制只能上传一个文件
    fileList = fileList.slice(-1);

    // 处理文件状态
    fileList = fileList.map(file => {
      if (file.response) {
        file.url = file.response.url;
      }
      return file;
    });

    setFileList(fileList);
  };

  // 上传前检查文件大小
  const beforeUpload = (file: File) => {
    const isLt200KB = file.size / 1024 < 200;
    if (!isLt200KB) {
      message.error('文件大小不能超过200KB!');
    }
    return isLt200KB || Upload.LIST_IGNORE;
  };

  return (
   <>
      <RepoBreadcrumb
        proName={projectName}
        customItems={[
          {
            path: '/tools',
            breadcrumbName: '工具套件',
          },
          {
            path: '/tools/repo',
            breadcrumbName: '代码',
          },
          {
            path: '/tools/repo/files',
            breadcrumbName: projectName,
          },
          {
            path: '/tools/repo/settings',
            breadcrumbName: '项目设置',
          },
        ]}
      />

      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>命名、主题和头像</Title>
            <Paragraph style={{ marginBottom: 0 }}>更新您的项目名称、主题、描述和头像。</Paragraph>
          </div>
          <Button onClick={() => setCollapsed(!collapsed)}>
            {collapsed ? '展开' : '折叠'}
          </Button>
        </div>

        {!collapsed && (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              projectName: projectDetail?.name || projectName,
              projectId: projectId,
              description: projectDetail?.description || '',
            }}
            disabled={loading}
          >
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '20px' }}>
              <div style={{ flex: '1', minWidth: '300px' }}>
                <Form.Item
                  label="项目名称"
                  name="projectName"
                  rules={[{ required: true, message: '请输入项目名称' }]}
                >
                  <Input style={{ width: '100%' }} />
                </Form.Item>
              </div>

              <div style={{ flex: '1', minWidth: '300px' }}>
                <Form.Item
                  label="项目 ID"
                  name="projectId"
                >
                  <Input style={{ width: '100%' }} disabled />
                </Form.Item>
              </div>
            </div>

            <Form.Item
              label="主题"
              name="topics"
            >
              <Input
                placeholder="搜索主题"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              label="项目描述（可选）"
              name="description"
            >
              <TextArea rows={4} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label="项目头像">
              <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                <div style={{
                  width: 80,
                  height: 80,
                  backgroundColor: '#f0f2f5',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 16,
                  fontSize: 32,
                  color: '#333',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px'
                }}>
                  {fileList.length > 0 ? (
                    <img
                      src={fileList[0].url || URL.createObjectURL(fileList[0].originFileObj as Blob)}
                      alt="头像"
                      style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                    />
                  ) : (
                    'W'
                  )}
                </div>
                <div>
                  <Upload
                    beforeUpload={beforeUpload}
                    onChange={handleFileChange}
                    fileList={fileList}
                    maxCount={1}
                    showUploadList={false}
                  >
                    <Button icon={<UploadOutlined />}>选择文件...</Button>
                  </Upload>
                  <div style={{ marginTop: 8, color: '#666' }}>
                    {fileList.length > 0 ? fileList[0].name : 'No file chosen.'}
                  </div>
                  <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                  最大文件大小为200 KB.
                  </div>
                </div>
              </div>
            </Form.Item>

            <Form.Item style={{ marginTop: 24 }}>
              <Button type="primary" htmlType="submit" size="middle" loading={loading}>
                保存更改
              </Button>
            </Form.Item>
          </Form>
        )}
      </Card>

      {/* 默认分支设置 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>默认分支</Title>
            <Paragraph style={{ marginBottom: 0 }}>设置此项目的默认分支。所有合并请求和提交都针对此分支，除非您指定其他分支。</Paragraph>
          </div>
          <Button onClick={() => setBranchCollapsed(!branchCollapsed)}>
            {branchCollapsed ? '展开' : '折叠'}
          </Button>
        </div>

        {!branchCollapsed && (
          <Form
            layout="vertical"
            initialValues={{
              defaultBranch: 'main',
              autoCloseIssues: false
            }}
          >
            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>默认分支</div>
              <Select
                style={{ width: '100%', maxWidth: 460 }}
                defaultValue="main"
              >
                <Option value="main">main</Option>
                <Option value="master">master</Option>
                <Option value="develop">develop</Option>
              </Select>
            </div>

            <Checkbox
              checked={autoCloseIssues}
              onChange={(e) => setAutoCloseIssues(e.target.checked)}
            >
              <span>自动关闭默认分支上的引用问题</span>
              <Tooltip title="当合并请求和提交在默认分支上关闭，任何他们引用的问题也会关闭">
                <QuestionCircleOutlined style={{ marginLeft: 8 }} />
              </Tooltip>
            </Checkbox>

            <div style={{ marginTop: 24 }}>
              <Button type="primary" size="middle">
                保存更改
              </Button>
            </div>
          </Form>
        )}
      </Card>

      {/* 镜像仓库设置 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>镜像仓库</Title>
            <Paragraph style={{ marginBottom: 0 }}>设置您的项目自动推送和/或拉取更改到/从另一个仓库。分支、标签和提交将被同步。</Paragraph>
          </div>
          <Button onClick={() => setMirrorCollapsed(!mirrorCollapsed)}>
            {mirrorCollapsed ? '展开' : '折叠'}
          </Button>
        </div>

        {!mirrorCollapsed && (
          <Form layout="vertical">
            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>Git 仓库 URL</div>
              <Input
                placeholder="输入远程仓库 URL"
                style={{ width: '100%' }}
              />
            </div>

            <div style={{ marginBottom: 16 }}>
              <ul style={{ paddingLeft: 20, color: '#666', fontSize: 14 }}>
                <li>仓库必须可通过 http://、https://、ssh:// 或 git:// 访问。</li>
                <li>使用 http:// 或 https:// 协议时，请提供仓库的确切 URL。HTTP 重定向将不会被遵循。</li>
                <li>包含凭据的 URL 将被拒绝。</li>
                <li>更新操作将在每次推送后排队。对于大型仓库，使用浅克隆可能会更好。</li>
                <li>如果是拉取镜像，您的用户将是所有活动的作者，这些活动是更新的结果，比如新分支的创建或新提交的推送到现有分支。</li>
              </ul>
            </div>

            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>镜像方向</div>
              <Select
                style={{ width: '100%', maxWidth: 460 }}
                defaultValue="push"
              >
                <Option value="push">推送</Option>
                <Option value="pull">拉取</Option>
              </Select>
            </div>

            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>认证方式</div>
              <Select
                style={{ width: '100%', maxWidth: 460 }}
                defaultValue="password"
              >
                <Option value="password">密码</Option>
                <Option value="ssh">SSH 密钥</Option>
              </Select>
            </div>

            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>密码</div>
              <Input.Password
                placeholder="输入密码"
                style={{ width: '100%', maxWidth: 460 }}
              />
            </div>

            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>密码</div>
              <Input.Password
                placeholder="确认密码"
                style={{ width: '100%', maxWidth: 460 }}
              />
            </div>

            <div style={{ marginBottom: 16 }}>
              <Checkbox
                checked={mirrorPushChanges}
                onChange={(e) => setMirrorPushChanges(e.target.checked)}
              >
                <span>仅推送受保护的分支</span>
                <Tooltip title="如果启用，只有受保护的分支将被同步">
                  <QuestionCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </Checkbox>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Checkbox
                checked={mirrorProtectedBranches}
                onChange={(e) => setMirrorProtectedBranches(e.target.checked)}
              >
                <span>镜像仅受保护的分支</span>
                <Tooltip title="如果启用，只有受保护的分支将被镜像">
                  <QuestionCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </Checkbox>
            </div>

            <div style={{ marginTop: 24 }}>
              <Button type="primary" size="middle">
                镜像仓库
              </Button>
            </div>
          </Form>
        )}
      </Card>

      {/* 受保护分支设置 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>受保护分支</Title>
            <Paragraph style={{ marginBottom: 0 }}>保持稳定分支安全并强制开发者使用合并请求。什么是<a href="#">受保护分支</a>？</Paragraph>
          </div>
          <Button onClick={() => setProtectedCollapsed(!protectedCollapsed)}>
            {protectedCollapsed ? '展开' : '折叠'}
          </Button>
        </div>

        {!protectedCollapsed && (
          <Form layout="vertical">
            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>保护一个分支</div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                <div>
                  <div style={{ marginBottom: 8 }}>分支</div>
                  <Select
                    style={{ width: '100%', maxWidth: 460 }}
                    placeholder="选择分支..."
                  >
                    <Option value="main">main</Option>
                    <Option value="master">master</Option>
                    <Option value="develop">develop</Option>
                  </Select>
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>通配符如 <code>*-stable</code> 或 <code>production/*</code> 被支持。</div>
                </div>

                <div>
                  <div style={{ marginBottom: 8 }}>允许合并</div>
                  <Select
                    style={{ width: '100%', maxWidth: 460 }}
                    defaultValue="maintainers"
                  >
                    <Option value="maintainers">维护者</Option>
                    <Option value="developers">开发者 + 维护者</Option>
                    <Option value="no_one">没有人</Option>
                  </Select>
                </div>

                <div>
                  <div style={{ marginBottom: 8 }}>允许推送</div>
                  <Select
                    style={{ width: '100%', maxWidth: 460 }}
                    defaultValue="maintainers"
                  >
                    <Option value="maintainers">维护者</Option>
                    <Option value="developers">开发者 + 维护者</Option>
                    <Option value="no_one">没有人</Option>
                  </Select>
                </div>

                <div>
                  <div style={{ marginBottom: 8 }}>允许强制推送</div>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Switch defaultChecked={false} />
                    <span style={{ marginLeft: 8 }}>允许所有有推送权限的用户强制推送</span>
                  </div>
                </div>
              </div>
            </div>

            <div style={{ marginTop: 24 }}>
              <Button type="primary" size="middle">
                保护
              </Button>
            </div>

            <Divider />

            <div style={{ marginTop: 24 }}>
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <div style={{ fontWeight: 'bold' }}>分支</div>
                  <div style={{ display: 'flex', gap: 16 }}>
                    <div style={{ fontWeight: 'bold' }}>允许合并</div>
                    <div style={{ fontWeight: 'bold' }}>允许推送</div>
                    <div style={{ fontWeight: 'bold' }}>允许强制推送</div>
                  </div>
                </div>
              </div>

              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>main</div>
                  <div style={{ display: 'flex', gap: 16 }}>
                    <div>维护者</div>
                    <div>维护者</div>
                    <div>
                      <Switch size="small" defaultChecked={false} disabled />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        )}
      </Card>

      {/* 合并请求设置 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>合并请求</Title>
            <Paragraph style={{ marginBottom: 0 }}>选择您的合并方法、合并选项、合并检查和合并建议。</Paragraph>
          </div>
          <Button onClick={() => setMergeRequestsCollapsed(!mergeRequestsCollapsed)}>
            {mergeRequestsCollapsed ? '展开' : '折叠'}
          </Button>
        </div>

        {!mergeRequestsCollapsed && (
          <Form layout="vertical">
            <div style={{ marginBottom: 24 }}>
              <Title level={5}>合并方法</Title>
              <Paragraph style={{ marginBottom: 16 }}>确定当您合并一个合并请求时会发生什么。<a href="#">它们有什么不同？</a></Paragraph>

              <div style={{ marginBottom: 16 }}>
                <Radio.Group
                  value={mergeMethod}
                  onChange={(e: any) => setMergeMethod(e.target.value)}
                  style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
                >
                  <Radio value="merge_commit">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>合并提交</div>
                      <div>每次合并创建一个合并提交。</div>
                    </div>
                  </Radio>
                  <Radio value="merge_commit_with_semi_linear_history">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>带半线性历史的合并提交</div>
                      <div>
                        每次合并创建一个合并提交。<br />
                        只有当分支是最新的或可以快进与目标分支时才允许合并。<br />
                        当半线性合并不可能时，用户可以选择变基。
                      </div>
                    </div>
                  </Radio>
                  <Radio value="fast_forward_merge">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>快进式合并</div>
                      <div>
                        仅当可以快进式合并时允许。<br />
                        仅快进式合并。<br />
                        当有合并冲突时，用户可以选择变基。<br />
                        合并请求只有在分支可以无冲突快进式合并时才能合并。<a href="#">什么是合并路径？</a>
                      </div>
                    </div>
                  </Radio>
                </Radio.Group>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>合并选项</Title>
              <Paragraph style={{ marginBottom: 16 }}>影响合并请求如何以及何时合并的额外设置。</Paragraph>

              <div style={{ marginBottom: 16 }}>
                <Checkbox>
                  自动合并当管道成功时的合并请求
                </Checkbox>
              </div>

              <div style={{ marginBottom: 16 }}>
                <Checkbox>
                  显示链接以从命令行创建或查看合并请求时推送到命令行
                </Checkbox>
              </div>

              <div style={{ marginBottom: 16 }}>
                <Checkbox defaultChecked>
                  启用“删除源分支”选项（默认）
                </Checkbox>
                <div style={{ marginLeft: 24, color: '#666' }}>
                  已完成的合并请求和受保护的分支不受影响。
                </div>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>合并时压缩提交</Title>
              <Paragraph style={{ marginBottom: 16 }}>设置此选项在合并请求中的默认行为。对此的更改也会应用到现有的合并请求。<a href="#">什么是压缩？</a></Paragraph>

              <div style={{ marginBottom: 16 }}>
                <Radio.Group
                  value={squashOption}
                  onChange={(e: any) => setSquashOption(e.target.value)}
                  style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
                >
                  <Radio value="do_not_allow">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>不允许</div>
                      <div>压缩从不执行，复选框是隐藏的。</div>
                    </div>
                  </Radio>
                  <Radio value="allow">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>允许</div>
                      <div>复选框可见且可选择，默认不选中。</div>
                    </div>
                  </Radio>
                  <Radio value="encourage">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>鼓励</div>
                      <div>复选框可见且可选择，默认选中。</div>
                    </div>
                  </Radio>
                  <Radio value="require">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>要求</div>
                      <div>压缩总是执行。复选框可见且选中，用户不能更改它。</div>
                    </div>
                  </Radio>
                </Radio.Group>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>合并检查</Title>
              <Paragraph style={{ marginBottom: 16 }}>这些检查必须通过才能合并合并请求。</Paragraph>

              <div style={{ marginBottom: 16 }}>
                <Checkbox>
                  <span>管道必须成功</span>
                  <div style={{ marginLeft: 24, color: '#666' }}>如果最新管道未成功或是运行中，合并请求将不能被合并。</div>
                </Checkbox>
              </div>

              <div style={{ marginBottom: 16 }}>
                <Checkbox>
                  <span>跳过管道被认为成功</span>
                  <div style={{ marginLeft: 24, color: '#666' }}>引入了合并失败的风险。</div>
                </Checkbox>
              </div>

              <div style={{ marginBottom: 16 }}>
                <Checkbox>
                  <span>所有线程必须解决</span>
                </Checkbox>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>合并建议</Title>
              <Paragraph style={{ marginBottom: 16 }}>应用合并请求建议时使用的提交消息。</Paragraph>

              <div style={{ marginBottom: 16 }}>
                <Input placeholder="应用 %{suggestions_count} 建议到 %{files_count} 文件" style={{ width: '100%' }} />
                <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>留空以使用默认模板。最大 255 个字符。<a href="#">我可以使用哪些变量？</a></div>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>合并提交消息模板</Title>
              <Paragraph style={{ marginBottom: 16 }}>合并时使用的提交消息，如果合并方法创建了合并提交。</Paragraph>

              <div style={{ marginBottom: 16 }}>
                <TextArea
                  rows={6}
                  placeholder="合并分支 '%{source_branch}' 到 '%{target_branch}'"
                  style={{ width: '100%' }}
                />
                <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>留空以使用默认模板。最大 500 个字符。<a href="#">我可以使用哪些变量？</a></div>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>压缩提交消息模板</Title>
              <Paragraph style={{ marginBottom: 16 }}>压缩提交时使用的提交消息。</Paragraph>

              <div style={{ marginBottom: 16 }}>
                <TextArea
                  rows={6}
                  placeholder="%{title}"
                  style={{ width: '100%' }}
                />
                <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>留空以使用默认模板。最大 500 个字符。<a href="#">我可以使用哪些变量？</a></div>
              </div>
            </div>

            <div style={{ marginTop: 24 }}>
              <Button type="primary" size="middle" onClick={() => form.submit()}>
                保存更改
              </Button>
            </div>
          </Form>
        )}
      </Card>

      {/* 可见性、项目功能、权限设置 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>可见性、项目功能、权限</Title>
            <Paragraph style={{ marginBottom: 0 }}>选择可见性级别、启用/禁用项目功能及其权限、禁用电子邮件通知和显示默认表情符号。</Paragraph>
          </div>
          <Button onClick={() => setVisibilityCollapsed(!visibilityCollapsed)}>
            {visibilityCollapsed ? '展开' : '折叠'}
          </Button>
        </div>

        {!visibilityCollapsed && (
          <Form layout="vertical">
            <div style={{ marginBottom: 24 }}>
              <Title level={5}>项目可见性</Title>
              <Paragraph style={{ marginBottom: 16 }}>管理谁可以在公共访问目录中看到项目。<a href="#">了解更多</a></Paragraph>

              <div style={{ marginBottom: 16 }}>
                <Select
                  style={{ width: '100%', maxWidth: 300 }}
                  value={projectVisibility}
                  onChange={(value) => setProjectVisibility(value)}
                >
                  <Option value="private">私有</Option>
                  <Option value="internal">内部</Option>
                  <Option value="public">公开</Option>
                </Select>
                <div style={{ marginTop: 8, color: '#666' }}>
                  该项目只能由项目成员访问。访问必须明确授予每个用户。
                </div>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>问题</Title>
              <Paragraph style={{ marginBottom: 16 }}>灵活工具，可协作开发想法并规划在这个项目中的工作。<a href="#">了解更多</a></Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={issuesEnabled}
                  onChange={(checked) => setIssuesEnabled(checked)}
                  style={{ marginRight: 16 }}
                />
                <Select
                  style={{ width: 200 }}
                  value={issuesEnabled ? "only_members" : "enable_feature"}
                  disabled={true}
                >
                  <Option value="only_members">只有项目成员</Option>
                  <Option value="enable_feature">Enable feature to choose access level</Option>
                </Select>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>仓库</Title>
              <Paragraph style={{ marginBottom: 16 }}>在这个项目中查看和编辑文件。</Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={repositoryEnabled}
                  onChange={(checked) => {
                    setRepositoryEnabled(checked);
                    if (!checked) {
                      // 如果仓库关闭，确保合并请求和CI/CD也是关闭状态
                      setMergeRequestsEnabled(false);
                      setCicdEnabled(false);
                    }
                  }}
                  style={{ marginRight: 16 }}
                />
                <Select
                  style={{ width: 200 }}
                  defaultValue="only_members"
                  disabled={!repositoryEnabled}
                >
                  <Option value="only_members">只有项目成员</Option>
                  <Option value="everyone">所有人</Option>
                </Select>
              </div>

              <div style={{ marginLeft: 40, marginBottom: 16 }}>
                <div style={{ marginBottom: 8 }}>合并请求</div>
                <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                  <Switch
                    checked={mergeRequestsEnabled}
                    onChange={(checked) => setMergeRequestsEnabled(checked)}
                    style={{ marginRight: 16 }}
                    disabled={!repositoryEnabled}
                  />
                  <div>提交变更以待合并审核。</div>
                </div>

                <div style={{ marginBottom: 8 }}>复制</div>
                <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                  <Switch
                    checked={forksEnabled}
                    onChange={(checked) => setForksEnabled(checked)}
                    style={{ marginRight: 16 }}
                    disabled={!repositoryEnabled}
                  />
                  <div>用户可以复制仓库到新项目。</div>
                </div>

                <div style={{ marginBottom: 8 }}>Git LFS 存储 (LFS)</div>
                <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                  <Switch
                    checked={lfsEnabled}
                    onChange={(checked) => setLfsEnabled(checked)}
                    style={{ marginRight: 16 }}
                    disabled={!repositoryEnabled}
                  />
                  <div>管理大型文件，如音频、视频和图形文件。<a href="#">了解更多</a></div>
                </div>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>包</Title>
              <Paragraph style={{ marginBottom: 16 }}>每个项目都可以有自己的空间来存储其包。注意：包注册表始终可见，当项目是公开的。<a href="#">了解更多</a></Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={packagesEnabled}
                  onChange={(checked) => setPackagesEnabled(checked)}
                  style={{ marginRight: 16 }}
                />
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>CI/CD</Title>
              <Paragraph style={{ marginBottom: 16 }}>构建、测试和部署您的更改。</Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={cicdEnabled}
                  onChange={(checked) => setCicdEnabled(checked)}
                  style={{ marginRight: 16 }}
                  disabled={!repositoryEnabled}
                />
                <Select
                  style={{ width: 200 }}
                  defaultValue="only_members"
                  disabled={!cicdEnabled || !repositoryEnabled}
                >
                  <Option value="only_members">只有项目成员</Option>
                </Select>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>分析</Title>
              <Paragraph style={{ marginBottom: 16 }}>查看项目分析。</Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={analyticsEnabled}
                  onChange={(checked) => setAnalyticsEnabled(checked)}
                  style={{ marginRight: 16 }}
                />
                <Select
                  style={{ width: 200 }}
                  defaultValue="only_members"
                  disabled={!analyticsEnabled}
                >
                  <Option value="only_members">只有项目成员</Option>
                </Select>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>安全与合规</Title>
              <Paragraph style={{ marginBottom: 16 }}>这个项目的安全与合规。</Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={securityEnabled}
                  onChange={(checked) => setSecurityEnabled(checked)}
                  style={{ marginRight: 16 }}
                />
                <Select
                  style={{ width: 200 }}
                  defaultValue="only_members"
                  disabled={!securityEnabled}
                >
                  <Option value="only_members">只有项目成员</Option>
                </Select>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>Wiki</Title>
              <Paragraph style={{ marginBottom: 16 }}>项目文档页面。</Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={wikiEnabled}
                  onChange={(checked) => setWikiEnabled(checked)}
                  style={{ marginRight: 16 }}
                />
                <Select
                  style={{ width: 200 }}
                  defaultValue="only_members"
                  disabled={!wikiEnabled}
                >
                  <Option value="only_members">只有项目成员</Option>
                  <Option value="everyone">所有人</Option>
                </Select>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>代码片段</Title>
              <Paragraph style={{ marginBottom: 16 }}>与项目外的他人共享代码。</Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={snippetsEnabled}
                  onChange={(checked) => setSnippetsEnabled(checked)}
                  style={{ marginRight: 16 }}
                />
                <Select
                  style={{ width: 200 }}
                  defaultValue="only_members"
                  disabled={!snippetsEnabled}
                >
                  <Option value="only_members">只有项目成员</Option>
                  <Option value="everyone">所有人</Option>
                </Select>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>运维</Title>
              <Paragraph style={{ marginBottom: 16 }}>配置您的项目资源并监控其健康状况。</Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={operationsEnabled}
                  onChange={(checked) => setOperationsEnabled(checked)}
                  style={{ marginRight: 16 }}
                />
                <Select
                  style={{ width: 200 }}
                  defaultValue="only_members"
                  disabled={!operationsEnabled}
                >
                  <Option value="only_members">只有项目成员</Option>
                </Select>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Title level={5}>指标仪表板</Title>
              <Paragraph style={{ marginBottom: 16 }}>可视化项目的性能指标。</Paragraph>

              <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                <Select
                  style={{ width: 200 }}
                  defaultValue="only_members"
                >
                  <Option value="only_members">只有项目成员</Option>
                </Select>
              </div>
            </div>

            <Divider />

            <div style={{ marginBottom: 16 }}>
              <Checkbox
                checked={disableEmails}
                onChange={(e) => setDisableEmails(e.target.checked)}
              >
                禁用电子邮件通知
              </Checkbox>
              <div style={{ marginLeft: 24, color: '#666' }}>
                覆盖所有项目成员的用户通知偏好。
              </div>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Checkbox
                checked={showEmoji}
                onChange={(e) => setShowEmoji(e.target.checked)}
              >
                显示默认表情符号
              </Checkbox>
              <div style={{ marginLeft: 24, color: '#666' }}>
                始终在问题、合并请求和代码片段上显示站起大拇指和站下大拇指表情符号按钮。
              </div>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Checkbox
                checked={showUnicode}
                onChange={(e) => setShowUnicode(e.target.checked)}
              >
                显示非正式支持的 Unicode 字符
              </Checkbox>
              <div style={{ marginLeft: 24, color: '#666' }}>
                突出显示隐藏的 Unicode 字符。这些字符对从右到左语言有无害的用途，但也可用于潜在的欺骗。
              </div>
            </div>

            <div style={{ marginTop: 24 }}>
              <Button type="primary" size="middle" onClick={() => form.submit()}>
                保存更改
              </Button>
            </div>
          </Form>
        )}
      </Card>
      </>
  );
};

export default ProjectSettings;