import CardList from '@/components/CardList/CardList';
import { listProjects } from '@/services/ant-design-pro/gitlab';
import {
  CommentOutlined,
  FileOutlined,
  ForkOutlined,
  FormOutlined,
  IssuesCloseOutlined,
  LockOutlined,
  PlusOutlined,
  StarFilled,
  UnlockOutlined,
} from '@ant-design/icons';
import { CheckCard, ModalForm, PageContainer } from '@ant-design/pro-components';
import { Avatar, Button, Form, FormInstance, GetProps, Input, List, Space, Tag, Tabs } from 'antd';
import { Key, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ListItemDataType } from './data';
import useStyles from './style.style';

import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import './style.css';
const getKey = (id: string, index: number) => `${id}-${index}`;
const { TabPane } = Tabs;
dayjs.extend(relativeTime);
const NewProjectModal = ({ form }: { form: FormInstance }) => {
  const [check, setCheck] = useState('');

  const navigate = useNavigate();
  const handleRedirect = async () => {
    console.log(check);
    if (check === 'blank') {
      navigate('/devops/projects/newPro');
    } else if (check === 'template') {
      navigate('/devops/projects/newProbyTemp');
    } else if (check === 'import') {
      navigate('/devops/projects/importPro');
    }
    return true;
  };
  return (
    <ModalForm
      title="新建项目"
      trigger={
        <Button type="primary">
          <PlusOutlined />
          新建项目
        </Button>
      }
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: () => console.log('run'),
      }}
      submitTimeout={2000}
      onFinish={handleRedirect}
    >
      <CheckCard.Group
        onChange={(value) => {
          setCheck(value);
        }}
        style={{ width: '100%' }}
      >
        <CheckCard
          title="新建空白项目"
          avatar={<PlusOutlined />}
          description="创建一个空白项目来存储文件、计划工作和协作代码等。"
          value="blank"
          style={{ height: '150px', margin: '30px' }}
        />
        <CheckCard
          title="新建模板项目"
          avatar={<PlusOutlined />}
          description="创建一个模板项目，以便快速开始。"
          value="template"
          style={{ height: '150px', margin: '30px' }}
        />
        <br />
        <CheckCard
          title="导入项目"
          avatar={<PlusOutlined />}
          description="从外部源（如Github、Bitbucket或GitLab的另一个实例）迁移项目。"
          value="import"
          style={{ height: '150px', marginLeft: '30px' }}
        />
      </CheckCard.Group>
    </ModalForm>
  );
};

const calculateTimeDiff = (dateString: string) => {
  const now = new Date();
  const targetDate = new Date(dateString);
  const diffInHours = Math.abs(now.getTime() - targetDate.getTime()) / (1000 * 60 * 60);

  if (diffInHours >= 24) {
    return `${Math.round(diffInHours / 24)}天`;
  } else {
    return `${Math.round(diffInHours)}小时`;
  }
};

export default () => {
  const { styles } = useStyles();

  const [projects, setProjects] = useState<API.ListProjects[]>([]);

  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedRowKeys, setExpandedRowKeys] = useState<readonly Key[]>([]);

  //监听搜索框
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredProjects, setFilteredProjects] = useState<API.ListProjects[]>([]);

  const [form] = Form.useForm<{}>();
  const [activeTab, setActiveTab] = useState('tab1');

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const fetchedProjects = await listProjects();
        setProjects(fetchedProjects);
        setFilteredProjects(fetchedProjects); // 初始化时显示所有项目
      } catch (error) {
        console.error('Failed to fetch projects:', error);
      }
    };

    fetchProjects();
  }, []);

  useEffect(() => {
    if (searchTerm === '') {
      setFilteredProjects(projects); // 如果没有搜索项，则显示所有项目
    } else {
      const filtered = projects.filter((project) =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredProjects(filtered); // 显示过滤后的项目
    }
  }, [searchTerm, projects]);

  const [timeoutId, setTimeoutId] = useState(null);

  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [timeoutId]);

  const proData = filteredProjects.map((project) => ({
    title: project.name,
    path: `${project.path_with_namespace}`,
    description: project.description,
    time: project.created_at,
    id: project.id,
    update: project.last_activity_at,
    defaultBranch: project.default_branch,
    visibility: project.visibility,
    star_count: project.star_count,
    forks_count: project.forks_count,
    open_issues_count: project.open_issues_count,
    owner: project.owner,
  }));

  type SearchProps = GetProps<typeof Input.Search>;
  const { Search } = Input;
  const onSearch: SearchProps['onSearch'] = (value, _e, info) => {
    // proName=value;
    // console.log(proName);
  };

  const cardInfo: ListItemDataType[] = [
    {
      id: '1',
      owner: '001xxx',
      title: 'DeepSeek',
      cover: require('../../assets/img/Banner/ds_logo.jpg'),
      status: 'normal',
      percent: 0,
      updatedAt: new Date(new Date().getTime() - 1000 * 60 * 60 * 2).getTime(),
      size: '631 B',
      filecount: '40 Files',
      commits: '188 Commits',
      description:
        '通义千问大模型‌是由阿里云自主研发的大语言模型，主要用于理解和分析用户输入的自然语言，提供高效、智能的语言服务体验。该模型具备强大的语言处理能力，能够进行文字创作、文本处理、编程辅助、翻译服务、对话模拟、数据可视化等多种任务。',
      activeUser: Math.ceil(Math.random() * 100000) + 100000,
      newUser: Math.ceil(Math.random() * 1000) + 1000,
      star: Math.ceil(Math.random() * 100) + 100,
      like: Math.ceil(Math.random() * 100) + 100,
      message: Math.ceil(Math.random() * 10) + 10,
      members: [
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png',
          name: '曲丽丽',
          id: 'member1',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png',
          name: '王昭君',
          id: 'member2',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png',
          name: '董娜娜',
          id: 'member3',
        },
      ],
    },
    {
      id: '2',
      owner: '001xxx',
      title: '通义千问 - QWen',
      cover: require('../../assets/img/Banner/qwen.jpeg'),
      status: 'normal',
      percent: 0,
      updatedAt: new Date(new Date().getTime() - 1000 * 60 * 60 * 2).getTime(),
      size: '13 B',
      filecount: '40 Files',
      commits: '188 Commits',
      description:
        '通义千问大模型‌是由阿里云自主研发的大语言模型，主要用于理解和分析用户输入的自然语言，提供高效、智能的语言服务体验。该模型具备强大的语言处理能力，能够进行文字创作、文本处理、编程辅助、翻译服务、对话模拟、数据可视化等多种任务。',
      activeUser: Math.ceil(Math.random() * 100000) + 100000,
      newUser: Math.ceil(Math.random() * 1000) + 1000,
      star: Math.ceil(Math.random() * 100) + 100,
      like: Math.ceil(Math.random() * 100) + 100,
      message: Math.ceil(Math.random() * 10) + 10,
      members: [
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png',
          name: '曲丽丽',
          id: 'member1',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png',
          name: '王昭君',
          id: 'member2',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png',
          name: '董娜娜',
          id: 'member3',
        },
      ],
    },
    {
      id: '3',
      owner: '002xxx',
      title: 'LLaMa',
      cover: require('../../assets/img/Banner/llama.jpg'),
      status: 'normal',
      percent: 0,
      updatedAt: new Date(new Date().getTime() - 1000 * 60 * 60 * 2 * 2).getTime(),
      size: '6 B',
      filecount: '40 Files',
      commits: '288 Commits',
      description:
        '，即大语言模型，是指使用大量文本数据训练的深度学习模型，可以生成自然语言文本或理解语言文本的含义。LLM模型可以处理多种自然语言任务，如文本分类、问答、对话等，是通向人工智能的重要途径。',
      activeUser: Math.ceil(Math.random() * 100000) + 100000,
      newUser: Math.ceil(Math.random() * 1000) + 1000,
      star: Math.ceil(Math.random() * 100) + 100,
      like: Math.ceil(Math.random() * 100) + 100,
      message: Math.ceil(Math.random() * 10) + 10,
      members: [
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png',
          name: '曲丽丽',
          id: 'member1',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png',
          name: '王昭君',
          id: 'member2',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png',
          name: '董娜娜',
          id: 'member3',
        },
      ],
    },
    {
      id: '4',
      owner: '003xxx',
      title: 'Gemma',
      cover: require('../../assets/img/Banner/gemma.jpeg'),
      status: 'normal',
      percent: 0,
      updatedAt: new Date(new Date().getTime() - 1000 * 60 * 3 * 8).getTime(),
      size: '6 B',
      filecount: '18 Files',
      commits: '53 Commits',
      description:
        '，即大语言模型，是指使用大量文本数据训练的深度学习模型，可以生成自然语言文本或理解语言文本的含义。LLM模型可以处理多种自然语言任务，如文本分类、问答、对话等，是通向人工智能的重要途径。',
      activeUser: Math.ceil(Math.random() * 100000) + 100000,
      newUser: Math.ceil(Math.random() * 1000) + 1000,
      star: Math.ceil(Math.random() * 100) + 100,
      like: Math.ceil(Math.random() * 100) + 100,
      message: Math.ceil(Math.random() * 10) + 10,
      members: [
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png',
          name: '曲丽丽',
          id: 'member1',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png',
          name: '王昭君',
          id: 'member2',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png',
          name: '董娜娜',
          id: 'member3',
        },
      ],
    },
    {
      id: '5',
      owner: '004xxx',
      title: 'Yolo-V5s',
      cover: require('../../assets/img/Banner/yolo.jpg'),
      status: 'normal',
      percent: 0,
      updatedAt: new Date(new Date().getTime() - 1000 * 60 * 60 * 2 * 5).getTime(),
      size: '7.2 MB',
      filecount: '2 Files',
      commits: '420 Commits',
      description:
        '，即大语言模型，是指使用大量文本数据训练的深度学习模型，可以生成自然语言文本或理解语言文本的含义。LLM模型可以处理多种自然语言任务，如文本分类、问答、对话等，是通向人工智能的重要途径。',
      activeUser: Math.ceil(Math.random() * 100000) + 100000,
      newUser: Math.ceil(Math.random() * 1000) + 1000,
      star: Math.ceil(Math.random() * 100) + 100,
      like: Math.ceil(Math.random() * 100) + 100,
      message: Math.ceil(Math.random() * 10) + 10,
      members: [
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png',
          name: '曲丽丽',
          id: 'member1',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png',
          name: '王昭君',
          id: 'member2',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png',
          name: '董娜娜',
          id: 'member3',
        },
      ],
    },
    {
      id: '6',
      owner: '004xxx',
      title: 'Resnet-50',
      cover: require('../../assets/img/Banner/resnet.jpeg'),
      status: 'normal',
      percent: 0,
      updatedAt: new Date(new Date().getTime() - (1000 * 60 * 60 * 2 * 8) / 3).getTime(),
      size: '98 MB',
      filecount: '2 Files',
      commits: '256 Commits',
      description:
        '，即大语言模型，是指使用大量文本数据训练的深度学习模型，可以生成自然语言文本或理解语言文本的含义。LLM模型可以处理多种自然语言任务，如文本分类、问答、对话等，是通向人工智能的重要途径。',
      activeUser: Math.ceil(Math.random() * 100000) + 100000,
      newUser: Math.ceil(Math.random() * 1000) + 1000,
      star: Math.ceil(Math.random() * 100) + 100,
      like: Math.ceil(Math.random() * 100) + 100,
      message: Math.ceil(Math.random() * 10) + 10,
      members: [
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png',
          name: '曲丽丽',
          id: 'member1',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png',
          name: '王昭君',
          id: 'member2',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png',
          name: '董娜娜',
          id: 'member3',
        },
      ],
    },
  ];

  const handleExperienceClick = () => {
    //   // 添加新路由
    //   addRoute({
    //     path: '/deepseek',
    //     name: 'DeepSeek',
    //     icon: 'smile',
    //     component: '../../deepseek'
    //   });
    //   // 更新菜单
    //   setInitialState((prevState) => ({
    //     ...prevState,
    //     menuData: [
    //       ...(prevState?.menuData || []),
    //       {
    //         path: '/deepseek',
    //         name: 'DeepSeek',
    //         icon: 'smile',
    //       }
    //     ]
    //   }));
  };

  const navigate = useNavigate();

  const handleTitleClick = (record: any) => {
    navigate('/tools/repo/files', {
      state: {
        proId: record.id,
        defaultBranch: record.defaultBranch,
        proName: record.title,
        ownerName: record.owner.name,
        update: record.update,
        visibility: record.visibility,
        starCount: record.star_count,
        forkCount: record.forks_count,
      },
    });
  };
  const paginationProps = {
    showSizeChanger: true,
    showQuickJumper: true,
    pageSize: 10,
    total: proData.length,
  };

  return (
    <PageContainer
      header={{
        ghost: true,
        extra: [
          <Search
            placeholder="搜索项目"
            onSearch={onSearch}
            style={{ width: 200 }}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />,
          // <Button key="1" icon={<SearchOutlined />} onClick={()=>handleSearch(searchTerm)}>搜索项目</Button>,
          <NewProjectModal form={form} />,
          // <Button key="0" onClick={handleButtonClick}>
          //     Try in your workspace {<RightOutlined />}</Button>,
        ],
      }}
      style={{
        height: 'calc(100vh - 48px)',
        overflow: 'hidden',
      }}
    >
      <div
        style={{
          height: 'calc(100vh - 130px)', // 减去头部高度和PageContainer的padding
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          borderRadius: '4px'
        }}
      >
        {/* 标签页导航 */}
        <div style={{
          flexShrink: 0, // 防止被压缩
          padding: '0 24px',
          height: '56px', // 固定Tabs高度
          display: 'flex',
          alignItems: 'center'
        }}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            type="card"
            style={{ width: '100%' }}
          >
            <TabPane key="tab1" tab="我参与的" />
            <TabPane key="tab2" tab="全部项目" />
            <TabPane key="tab3" tab="已归档" />
          </Tabs>
        </div>

        {/* 标签页内容区域 */}
        <div
          style={{
            flex: 1, // 占据剩余空间
            overflow: 'auto',
            padding: '8px 24px 8px 24px', // 增加底部padding
            scrollPaddingBottom: '8px', // 滚动时底部留白
          }}
        >
          {activeTab === 'tab1' && (
            <List
              size="large"
              rowKey="id"
              pagination={paginationProps}
              dataSource={proData}
              renderItem={(item) => (
                <List.Item
                  actions={[<span>{calculateTimeDiff(item.update?.toString() || '')}前</span>]}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar size="large" style={{ backgroundColor: '#87d068' }}>
                        {item.title?.charAt(0).toUpperCase() || 'P'}
                      </Avatar>
                    }
                    title={[
                      <Space size={12}>
                        <a onClick={() => handleTitleClick(item)}>{item.title}</a>
                        {item.visibility === 'private' ? <LockOutlined /> : <UnlockOutlined />}
                        {item.owner?.id === item.id ? (
                          <Tag color="geekblue">Owner</Tag>
                        ) : (
                          <Tag color="lime">Developer</Tag>
                        )}
                      </Space>,
                    ]}
                    description={item.path}
                  />
                  <div
                    style={{
                      minWidth: 400,
                      flex: 1,
                      display: 'flex',
                      justifyContent: 'flex-end',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}
                  >
                    <div
                      style={{
                        width: '200px',
                        position: 'absolute',
                        right: '20%',
                      }}
                    >
                      <Space size={0}>
                        <Tag>
                          <StarFilled />
                          {item.star_count}
                        </Tag>
                        <Tag>
                          <ForkOutlined />
                          {item.forks_count}
                        </Tag>
                        <Tag>
                          <IssuesCloseOutlined />
                          {item.open_issues_count}
                        </Tag>
                      </Space>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          )}

          {activeTab === 'tab2' && (
            <div className={styles.coverCardList}>
              <div className={styles.cardList}>
                <CardList
                  cardInfo={cardInfo}
                  button1="立即体验"
                  button2="查看详情"
                  button1Click={handleExperienceClick}
                  button2Click={handleExperienceClick}
                  icon1={<FormOutlined />}
                  icon2={<FileOutlined />}
                  icon3={<CommentOutlined />}
                  cardGrid={{
                    gutter: 20,
                    xs: 1,
                    sm: 2,
                    md: 3,
                    lg: 3,
                    xl: 4,
                    xxl: 4,
                  }}
                />
              </div>
            </div>
          )}

          {activeTab === 'tab3' && (
            <div className={styles.coverCardList}>
              <div className={styles.cardList}>
                <CardList
                  cardInfo={cardInfo}
                  button1="立即体验"
                  button2="查看详情"
                  button1Click={handleExperienceClick}
                  button2Click={handleExperienceClick}
                  icon1={<FormOutlined />}
                  icon2={<FileOutlined />}
                  icon3={<CommentOutlined />}
                  cardGrid={{
                    gutter: 20,
                    xs: 1,
                    sm: 2,
                    md: 3,
                    lg: 3,
                    xl: 4,
                    xxl: 4,
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </PageContainer>
  );
};
