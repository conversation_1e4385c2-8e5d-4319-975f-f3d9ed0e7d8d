
/* 现代化亮色主题样式 */

/* 亮色主题下的局部橘色晕染背景效果 */
body[data-theme="light"] .ant-pro-layout-content::before {
  content: '';
  position: absolute;
  top: -150px;
  right: -200px;
  width: 1200px;
  height: 900px;
  background:
    radial-gradient(ellipse 500px 400px at 70% 30%,
      rgba(255, 165, 0, 0.08) 0%,
      rgba(255, 140, 0, 0.06) 15%,
      rgba(255, 69, 0, 0.04) 30%,
      rgba(255, 165, 0, 0.03) 45%,
      rgba(255, 140, 0, 0.02) 60%,
      rgba(255, 165, 0, 0.01) 75%,
      rgba(255, 140, 0, 0.005) 85%,
      rgba(255, 165, 0, 0.002) 92%,
      transparent 100%
    ),
    radial-gradient(ellipse 400px 300px at 80% 20%,
      rgba(255, 165, 0, 0.06) 0%,
      rgba(255, 140, 0, 0.04) 20%,
      rgba(255, 69, 0, 0.03) 35%,
      rgba(255, 165, 0, 0.02) 50%,
      rgba(255, 140, 0, 0.01) 65%,
      rgba(255, 165, 0, 0.005) 80%,
      rgba(255, 140, 0, 0.002) 90%,
      transparent 100%
    ),
    radial-gradient(ellipse 600px 500px at 60% 40%,
      rgba(255, 165, 0, 0.04) 0%,
      rgba(255, 140, 0, 0.02) 30%,
      rgba(255, 69, 0, 0.01) 60%,
      rgba(255, 165, 0, 0.005) 80%,
      transparent 100%
    ) !important;
  pointer-events: none !important;
  z-index: 1 !important;
  /* animation: orangeGlowMove 60s ease-in-out infinite !important; */
  filter: blur(3px) !important;
  transition: all 0.5s ease !important;
}

/* 确保布局容器有相对定位 */
body[data-theme="light"] .ant-pro-layout-content {
  position: relative !important;
  overflow: hidden !important;
}



/* 平行移动动画效果 - 从右上角向左移动，到中间后向右移动 */
@keyframes orangeGlowMove {
  0% {
    opacity: 0.6;
    transform: translateX(0px);
  }
  25% {
    opacity: 0.8;
    transform: translateX(-450px);
  }
  50% {
    opacity: 1;
    transform: translateX(-900px);
  }
  75% {
    opacity: 0.8;
    transform: translateX(-450px);
  }
  100% {
    opacity: 0.6;
    transform: translateX(0px);
  }
}

/* 确保登录页面不显示晕染效果 */
body[data-theme="light"] .login-container {
  position: relative;
  z-index: 1001;
}



/* 在登录页面时隐藏晕染效果 */
body[data-theme="light"]:has(.login-container) .ant-pro-layout-content::before {
  display: none !important;
}

/* 全局过渡效果 - 优化主题切换 */
* {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease !important;
}

/* 侧边栏样式 */
.ant-pro-sider{
  border-right: 0;
  /* background: #f7f7f7 !important; */
}

/* 菜单项样式 */
.ant-menu-item-selected{
  color: #1f2937 !important;
  font-weight: 600;
}

/* 菜单项基础样式 */
.ant-menu-light .ant-menu-item,
.ant-menu-light .ant-menu-submenu-title {
  border-radius: 6px;
  /* margin: 1px 12px; */
  /* width: calc(100% - 24px); */
  color: #6b7280;
  font-weight: 500;
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  border-bottom: none;
}

/* 选中状态 */
.ant-menu-light .ant-menu-item-selected{
  background-color: #ECECEC !important;
  border: 1.5px solid #d8d8d8;
  color: #1f2937;
  border-radius: 6px;
  /* margin: 1px 12px; */
  /* width: calc(100% - 24px); */
  font-weight: 600;
}

/* 悬停状态 */
.ant-menu-light .ant-menu-item:hover,
.ant-menu-light .ant-menu-submenu-title:hover {
  background-color: #f1f5f9 !important;
  border: 1.5px solid #ECECEC;
  color: #1f2937;
}

/* 菜单分割线 */
.ant-menu-light .ant-menu-item::after {
  display: none;
}

/* 移除默认边框 */
.ant-menu-light {
  border-right: none;
}

/* 菜单图标样式 */
.ant-menu-light .ant-menu-item .anticon,
.ant-menu-light .ant-menu-submenu-title .anticon {
  margin-right: 8px;
  font-size: 16px;
}

/* 菜单容器样式 */
.ant-menu-light.ant-menu-root {
  /* background: #f7f7f7; */
  border-right: none;
  padding: 8px 0;
}


.ant-menu-light .ant-menu-submenu > .ant-menu-submenu-title {
  margin: 0;
  width: 100%;
}

/* 子菜单展开后的样式 */
.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline{
  background: #f7f7f7;
  padding: 4px 0;
}

.ant-menu-light .ant-menu-sub .ant-menu-item {
  width: calc(100% - 36px);
  padding-left: 24px;
}

/* 移除菜单项的默认边框和分割线 */
.ant-menu-light .ant-menu-item::after,
.ant-menu-light .ant-menu-submenu-title::after {
  display: none;
}

/* 确保菜单项文字垂直居中 */
.ant-menu-light .ant-menu-item,
.ant-menu-light .ant-menu-submenu-title {
  display: flex;
  align-items: center;
}

/* 卡片样式 */
.ant-pro-card, .ant-card{
  border-radius: 8px;
  background-color: #ffffff;
  color: #1f2937;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  transition: box-shadow 0.2s ease;
}

/* PageContainer样式 */
.ant-pro-page-container{
  border-radius: 8px;
  background-color: #ffffff;
  color: #1f2937;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  transition: box-shadow 0.2s ease;
}

/* 卡片悬停效果 */
.ant-pro-card:hover, .ant-card:hover{
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12), 0 2px 4px 0 rgba(0, 0, 0, 0.08);
}

.ant-pro-card .ant-pro-card-title,
.ant-pro-table-list-toolbar-title{
  color: #1f2937;
  font-weight: 600;
}

/* 布局容器 - 移除直接设置，通过token控制 */

.ant-pro-layout .ant-pro-sider-actions{
  border-top: 1px solid #e2e8f0;
  background: #f7f7f7;
}

/* 标签页样式 */
.ant-tabs .ant-tabs-ink-bar{
  background: #ECECEC;
}

.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active{
  background: #ECECEC;
  color: #ffffff;
  border-radius: 6px 6px 0 0;
}

.ant-tabs .ant-tabs-tab{
  color: #6b7280;
  font-weight: 500;
}

.ant-tabs .ant-tabs-tab:hover{
  color: #1f2937;
}

/* 侧边栏触发器 */
.ant-layout-sider .ant-layout-sider-trigger{
  color: #6b7280;
  background: #ffffff;
  border-right: 1px solid #e2e8f0;
  height: 48px;
  border-radius: 0;
}

.ant-layout-sider .ant-layout-sider-trigger:hover{
  color: #1f2937;
  background: #f7f7f7;
}

/* 子菜单背景 */
.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline{
  background: #f7f7f7;
}

/* 链接颜色 */
a{
  color: #7BB3FE;
  font-weight: 500;
}

a:hover{
  color: #2563eb;
}

/* 列表样式 */
.ant-card-body .ant-list{
  background-color: #ffffff;
}

/* 统计卡片样式 */
.ant-statistic-title {
  color: #6b7280;
  font-weight: 500;
}

.ant-statistic-content {
  color: #1f2937;
  font-weight: 700;
}

/* 按钮样式 */
.ant-btn-primary {
  background: #ECECEC;
  border-color: #ECECEC;
  border-radius: 8px;
  font-weight: 500;
}

.ant-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* 输入框样式 */
.ant-input, .ant-select-selector {
  border-radius: 8px;
  border-color: #d1d5db;
}

.ant-input:focus, .ant-select-focused .ant-select-selector {
  border-color: #ECECEC;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 表格样式 */
.ant-table {
  border-radius: 12px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background: #f7f7f7;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

/* 进度条样式 */
.ant-progress-bg {
  background: #ECECEC;
}

/* 标签样式 */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
}

/* 统计数字卡片样式 - 匹配LogisticPro */
.ant-statistic {
  text-align: left;
}

.ant-statistic-content-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.ant-statistic-title {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

/* 统计卡片图标样式 */
.statistic-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 16px;
}

/* 不同类型统计卡片的图标背景色 */
.statistic-card-icon.orders {
  background: #f0f9ff;
  color: #0ea5e9;
}

.statistic-card-icon.shipment {
  background: #f0fdf4;
  color: #22c55e;
}

.statistic-card-icon.revenue {
  background: #fef3c7;
  color: #f59e0b;
}

.statistic-card-icon.inventory {
  background: #fdf2f8;
  color: #ec4899;
}

/* 图表容器样式 */
.chart-container {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 页面标题样式 */
.page-header-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

/* 面包屑样式 */
.ant-breadcrumb {
  margin-bottom: 16px;
}

.ant-breadcrumb a {
  color: #6b7280;
  text-decoration: none;
}

.ant-breadcrumb a:hover {
  color: #ECECEC;
}

/* 搜索框样式 */
.ant-input-search .ant-input-group .ant-input-affix-wrapper {
  border-radius: 8px 0 0 8px;
}

.ant-input-search .ant-input-search-button {
  border-radius: 0 8px 8px 0;
  background: #ECECEC;
  border-color: #ECECEC;
}

/* 分页样式 */
.ant-pagination {
  text-align: center;
  margin-top: 24px;
}

.ant-pagination-item-active {
  background: #ECECEC;
  border-color: #ECECEC;
}

.ant-pagination-item-active a {
  color: #ffffff;
}

/* 模态框样式 */
.ant-modal-content {
  border-radius: 12px;
}

.ant-modal-header {
  border-radius: 12px 12px 0 0;
  background: #f7f7f7;
  border-bottom: 1px solid #e5e7eb;
}

/* 抽屉样式 */
.ant-drawer-content {
  background: #f7f7f7;
}

.ant-drawer-body {
  background: #ffffff;
  margin: 16px;
  border-radius: 12px;
  padding: 24px;
}

/* 通知样式 */
.ant-notification {
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 工具提示样式 */
.ant-tooltip-inner {
  background: #1f2937;
  border-radius: 6px;
}

/* 下拉菜单样式 */
.ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.ant-dropdown-menu-item:hover {
  background: #f1f5f9;
}

/* 加载状态样式 */
.ant-spin-dot-item {
  background-color: #ECECEC;
}

/* 空状态样式 */
.ant-empty-description {
  color: #6b7280;
}