/* 现代化暗色主题样式 - 采用light主题布局但使用dark配色方案 */

/* 全局过渡效果 - 优化主题切换 */
* {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease !important;
}

/* 侧边栏样式 */
.ant-pro-sider{
  border-right: 0;
}

/* 菜单项样式 */
.ant-menu-item-selected{
  color: #FFFFFF !important;
  font-weight: 600;
}

/* 菜单项基础样式 */
.ant-menu-light .ant-menu-item,
.ant-menu-light .ant-menu-submenu-title {
  border-radius: 6px;
  color: #E1E1E1;
  font-weight: 500;
  height: 36px;
  line-height: 36px;
  border-bottom: none;
}

/* 选中状态 */
.ant-menu-light .ant-menu-item-selected{
  background-color: #7F7CFF !important;
  border: 1.5px solid #7F7CFF;
  color: #FFFFFF;
  border-radius: 6px;
  font-weight: 600;
}

/* 悬停状态 */
.ant-menu-light .ant-menu-item:hover,
.ant-menu-light .ant-menu-submenu-title:hover {
  background-color: #ADB5FF !important;
  border: 1.5px solid #ADB5FF;
  color: #FFFFFF;
}

/* 菜单分割线 */
.ant-menu-light .ant-menu-item::after {
  display: none;
}

/* 移除默认边框 */
.ant-menu-light {
  border-right: none;
}

/* 菜单图标样式 */
.ant-menu-light .ant-menu-item .anticon,
.ant-menu-light .ant-menu-submenu-title .anticon {
  margin-right: 8px;
  font-size: 16px;
}

.ant-menu-light .ant-menu-item,
.ant-menu-light .ant-menu-submenu-title {
  padding: 0 12px;
}

/* 菜单容器样式 */
.ant-menu-light.ant-menu-root {
  background: #1e1f23;
  border-right: none;
  padding: 8px 0;
}

.ant-menu-light .ant-menu-submenu > .ant-menu-submenu-title {
  margin: 0;
  width: 100%;
}

/* 子菜单展开后的样式 */
.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline{
  background: #1e1f23;
  padding: 4px 0;
}

.ant-menu-light .ant-menu-sub .ant-menu-item {
  width: calc(100% - 36px);
  padding-left: 24px;
}

/* 移除菜单项的默认边框和分割线 */
.ant-menu-light .ant-menu-item::after,
.ant-menu-light .ant-menu-submenu-title::after {
  display: none;
}

/* 确保菜单项文字垂直居中 */
.ant-menu-light .ant-menu-item,
.ant-menu-light .ant-menu-submenu-title {
  display: flex;
  align-items: center;
}

/* 卡片样式 */
.ant-pro-card, .ant-card{
  border-radius: 8px;
  background-color: #1E1F23;
  color: #E6E3FF;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid #2A2B2F;
  transition: box-shadow 0.2s ease;
}

/* PageContainer样式 */
.ant-pro-page-container{
  border-radius: 8px;
  background-color: #1E1F23;
  color: #E6E3FF;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid #2A2B2F;
  transition: box-shadow 0.2s ease;
}

/* 卡片悬停效果 */
.ant-pro-card:hover, .ant-card:hover{
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.4), 0 2px 4px 0 rgba(0, 0, 0, 0.3);
}

.ant-pro-card .ant-pro-card-title,
.ant-pro-table-list-toolbar-title{
  color: #FFFFFF;
  font-weight: 600;
}

/* 布局容器 - 移除直接设置，通过token控制 */

.ant-pro-layout .ant-pro-sider-actions{
  border-top: 1px solid #2A2B2F;
  background: #15161A;
}

/* 标签页样式 */
.ant-tabs .ant-tabs-ink-bar{
  background: #7F7CFF;
}

.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active{
  background: #7F7CFF;
  color: #FFFFFF;
  border-radius: 6px 6px 0 0;
}

.ant-tabs .ant-tabs-tab{
  color: #E1E1E1;
  font-weight: 500;
}

.ant-tabs .ant-tabs-tab:hover{
  color: #FFFFFF;
}

/* 侧边栏触发器 */
.ant-layout-sider .ant-layout-sider-trigger{
  color: #E6E3FF;
  background: #15161A;
  border-right: 1px solid #2A2B2F;
  height: 48px;
  border-radius: 0;
}

.ant-layout-sider .ant-layout-sider-trigger:hover{
  color: #FFFFFF;
  background: #1E1F23;
}

/* 子菜单背景 */
.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline{
  background: #15161A;
}

/* 链接颜色 */
a{
  color: #7F7CFF;
  font-weight: 500;
}

a:hover{
  color: #adb5ff;
}

/* 列表样式 */
.ant-card-body .ant-list{
  background-color: #1E1F23;
}

/* 统计卡片样式 */
.ant-statistic-title {
  color: #E6E3FF;
  font-weight: 500;
}

.ant-statistic-content {
  color: #FFFFFF;
  font-weight: 700;
}

/* 按钮样式 */
.ant-btn-primary {
  background: #ADB5FF;
  border-color: #ADB5FF;
  border-radius: 8px;
  font-weight: 500;
}

.ant-btn-primary:hover {
  background: #ADB5FF;
  border-color: #ADB5FF;
}

/* 输入框样式 */
.ant-input, .ant-select-selector {
  border-radius: 8px;
  border-color: #2A2B2F;
  background-color: #1E1F23;
  color: #E6E3FF;
}

.ant-input:focus, .ant-select-focused .ant-select-selector {
  border-color: #ADB5FF;
  box-shadow: 0 0 0 2px rgba(103, 97, 255, 0.2);
}
.ant-page-header .ant-page-header-heading-title,.ant-pagination,.ant-pagination .ant-pagination-disabled .ant-pagination-item-link,.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn
,.ant-list .ant-list-item .ant-list-item-meta .ant-list-item-meta-description,.ant-list .ant-list-item .ant-list-item-action>li
{
  color:#fafafa;
}
.ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector,.ant-input-affix-wrapper{
  background: #2a2b2f;
  border:0;
}
.ant-tabs-card >.ant-tabs-nav .ant-tabs-tab{
  border : 1px solid gray;
}

/* 表格样式 */
 .ant-table {
  border-radius: 12px;
  overflow: hidden;
  background-color: #1E1F23;
}

.ant-table-wrapper .ant-table-thead > tr > th {
  background: #15161A;
  color: #FFFFFF;
  font-weight: 600;
  border-bottom: 1px solid #2A2B2F;
}

.ant-table-tbody > tr > td {
  background-color: #1E1F23;
  color: #E6E3FF;
  border-bottom: 1px solid #2A2B2F;
}

.ant-table-tbody > tr:hover > td {
  background-color: #252629;
}

/* 进度条样式 */
.ant-progress-bg {
  background: #ADB5FF;
}

/* 标签样式 */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
  background-color: #1E1F23;
  color: #E6E3FF;
  border: 1px solid #2A2B2F;
}

/* 统计数字卡片样式 */
.ant-statistic {
  text-align: left;
}

.ant-statistic-content-value {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 1.2;
}

.ant-statistic-title {
  font-size: 13px;
  color: #E1E1E1;
  margin-bottom: 4px;
  font-weight: 500;
}

/* 统计卡片图标样式 */
.statistic-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 16px;
}

/* 不同类型统计卡片的图标背景色 - 暗色主题版本 */
.statistic-card-icon.orders {
  background: #1A2332;
  color: #6BB6FF;
}

.statistic-card-icon.shipment {
  background: #1A2E1F;
  color: #4ADE80;
}

.statistic-card-icon.revenue {
  background: #2E2419;
  color: #FBBF24;
}

.statistic-card-icon.inventory {
  background: #2E1A26;
  color: #F472B6;
}

/* 图表容器样式 */
.chart-container {
  background: #1E1F23;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

/* 页面标题样式 */
.page-header-title {
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 8px;
}

/* 面包屑样式 */
.ant-breadcrumb {
  margin-bottom: 16px;
}

.ant-breadcrumb a {
  color: #E1E1E1;
  text-decoration: none;
}

.ant-breadcrumb a:hover {
  color: #ADB5FF;
}

/* 搜索框样式 */
.ant-input-search .ant-input-group .ant-input-affix-wrapper {
  border-radius: 8px 0 0 8px;
  background-color: #1E1F23;
  border-color: #2A2B2F;
}

.ant-input-search .ant-input-search-button {
  border-radius: 0 8px 8px 0;
  background: #ADB5FF;
  border-color: #ADB5FF;
}

/* 分页样式 */
.ant-pagination {
  text-align: center;
  margin-top: 24px;
}

.ant-pagination-item {
  background-color: #1E1F23;
  border-color: #2A2B2F;
}

.ant-pagination-item a {
  color: #E6E3FF;
}

.ant-pagination-item-active {
  background: #ADB5FF;
  border-color: #ADB5FF;
}

.ant-pagination-item-active a {
  color: #FFFFFF;
}

/* 模态框样式 */
.ant-modal-content {
  border-radius: 12px;
  background-color: #1E1F23;
}

.ant-modal-header {
  border-radius: 12px 12px 0 0;
  background: #15161A;
  border-bottom: 1px solid #2A2B2F;
}

.ant-modal-title {
  color: #FFFFFF;
}

.ant-modal-body {
  color: #E6E3FF;
}

/* 抽屉样式 */
.ant-drawer-content {
  background: #15161A;
}

.ant-drawer-body {
  background: #1E1F23;
  margin: 16px;
  border-radius: 12px;
  padding: 24px;
}

/* 通知样式 */
.ant-notification {
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  background-color: #1E1F23;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  background: #1E1F23;
  border-radius: 6px;
  color: #E6E3FF;
}

/* 下拉菜单样式 */
.ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  background-color: #1E1F23;
}

.ant-dropdown-menu-item {
  color: #E6E3FF;
}

.ant-dropdown-menu-item:hover {
  background: #252629;
  color: #FFFFFF;
}

/* 加载状态样式 */
.ant-spin-dot-item {
  background-color: #ADB5FF;
}

/* 空状态样式 */
.ant-empty-description {
  color: #E6E3FF;
}

/* 表单样式 */
.ant-form-item-label > label {
  color: #E6E3FF;
}

.ant-form-item-extra {
  color: #E6E3FF;
}

/* 选择器样式 */
.ant-select-dropdown {
  background-color: #1E1F23;
}

.ant-select-item {
  color: #E6E3FF;
}

.ant-select-item-option-selected {
  background-color: #ADB5FF;
  color: #FFFFFF;
}

.ant-select-item:hover {
  background-color: #252629;
}

/* 日期选择器样式 */
.ant-picker-dropdown {
  background-color: #1E1F23;
}

.ant-picker-cell {
  color: #E6E3FF;
}

.ant-picker-cell:hover {
  background-color: #252629;
}

.ant-picker-cell-selected {
  background-color: #ADB5FF;
  color: #FFFFFF;
}

/* 复选框和单选框样式 */
.ant-checkbox-wrapper,
.ant-radio-wrapper {
  color: #E6E3FF;
}

.ant-checkbox-checked .ant-checkbox-inner,
.ant-radio-checked .ant-radio-inner {
  background-color: #ADB5FF;
  border-color: #ADB5FF;
}

/* 开关样式 */
.ant-switch-checked {
  background-color: #ADB5FF;
}

/* 滑块样式 */
.ant-slider-track {
  background-color: #ADB5FF;
}

.ant-slider-handle {
  border-color: #ADB5FF;
}

/* 评分样式 */
.ant-rate-star-full .ant-rate-star-first,
.ant-rate-star-full .ant-rate-star-second {
  color: #ADB5FF;
}

/* 步骤条样式 */
.ant-steps-item-finish .ant-steps-item-icon {
  background-color: #ADB5FF;
  border-color: #ADB5FF;
}

.ant-steps-item-process .ant-steps-item-icon {
  background-color: #ADB5FF;
  border-color: #ADB5FF;
}

/* 时间轴样式 */
.ant-timeline-item-head {
  background-color: #ADB5FF;
}

/* 锚点样式 */
.ant-anchor-link-active > .ant-anchor-link-title {
  color: #ADB5FF;
}

/* 回到顶部样式 */
.ant-back-top {
  background-color: #ADB5FF;
}

/* 文字排版样式 */
.ant-typography,
.ant-typography h1,
.ant-typography h2,
.ant-typography h3,
.ant-typography h4,
.ant-typography h5,
.ant-typography h6 {
  color: #E1E1E1;
}

.ant-typography.ant-typography-secondary {
  color: #A0A0A0;
}

/* 分割线样式 */
.ant-divider {
  border-color: #2A2B2F;
}

/* 警告框样式 */
.ant-alert {
  background-color: #1E1F23;
  border-color: #2A2B2F;
  color: #E6E3FF;
}

/* 滚动条样式 - Dark主题 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #15161A;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #2A2B2F;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #3A3B3F;
}

::-webkit-scrollbar-corner {
  background: #15161A;
}

/* Firefox滚动条样式 */
* {
  scrollbar-color: #2A2B2F #15161A;
}