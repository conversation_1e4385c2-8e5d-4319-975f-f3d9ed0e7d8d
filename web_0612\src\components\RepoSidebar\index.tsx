import { getTheme } from '@/components/RightContent/themeSwitcher';
import {
  AuditOutlined,
  BranchesOutlined,
  CodepenOutlined,
  FileOutlined,
  FlagOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MergeOutlined,
  TagOutlined,
  ToolOutlined,
  HistoryOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Menu } from 'antd';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

type MenuItem = Required<MenuProps>['items'][number];

interface RepoSidebarProps {
  collapsed?: boolean;
  onCollapse?: () => void;
}

const RepoSidebar: React.FC<RepoSidebarProps> = ({ collapsed = false, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>(getTheme() || 'dark');

  // Monitor theme changes
  useEffect(() => {
    // Initialize theme
    setCurrentTheme(getTheme());

    // Listen for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          setCurrentTheme(getTheme());
        }
      });
    });

    observer.observe(document.body, { attributes: true });

    return () => {
      observer.disconnect();
    };
  }, []);



  const items: MenuItem[] = [
    {
      key: '/tools/repo',
      icon: <CodepenOutlined />,
      label: '代码仓库',
    },

    {
      key: '/tools/repo/files',
      icon: <FileOutlined />,
      label: '文件管理',
    },
    {
      key: '/tools/repo/branch',
      icon: <BranchesOutlined />,
      label: '分支管理',
    },
    {
      key: '/tools/repo/commits',
      icon: <AuditOutlined />,
      label: '提交管理',
    },
    {
      key: '/tools/repo/compare',
      icon: <FlagOutlined />,
      label: '比较修订版本',
    },
    {
      key: '/tools/repo/tags',
      icon: <TagOutlined />,
      label: '标签',
    },
    {
      key: '/tools/repo/mergeRequests',
      icon: <MergeOutlined />,
      label: '合并请求',
    },
    {
      key: '/tools/repo/settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key.startsWith('/')) {
      navigate(key, { state: location.state });
    }
  };

  // 获取当前应该高亮的菜单项
  const getSelectedMenuKey = () => {
    const { pathname } = location;

    // 处理子路由情况
    if (pathname.startsWith('/tools/repo/compare/compare_result')) {
      return '/tools/repo/compare';
    }

    // 处理新建项目页面，高亮"仓库"菜单项
    if (pathname.startsWith('/tools/repo/newProject')) {
      return '/tools/repo';
    }

    if (pathname.startsWith('/tools/repo/commits/newMergeRequest') || pathname.startsWith('/tools/repo/newMergeRequest')) {
      return '/tools/repo/mergeRequests';
    }

    // 处理新建文件页面，高亮"文件管理"菜单项
    if (pathname.startsWith('/tools/repo/files/newFile')) {
      return '/tools/repo/files';
    }

    // 处理新建标签页面，高亮"标签"菜单项
    if (pathname.startsWith('/tools/repo/newTag')) {
      return '/tools/repo/tags';
    }

    // 处理活动页面
    if (pathname.startsWith('/tools/repo/activities')) {
      return '/tools/repo/activities';
    }

    // 默认返回当前路径
    return pathname;
  };

  // 我们不再需要这个状态，因为它在组件中没有被使用
  // 如果将来需要使用，可以取消注释
  // const [mainMenuCollapsed, setMainMenuCollapsed] = useState(false);

  // // 检测主菜单的折叠状态
  // useEffect(() => {
  //   const checkMainMenuCollapsed = () => {
  //     // 获取主菜单宽度，判断是否折叠
  //     const mainMenu = document.querySelector('.ant-pro-sider');
  //     if (mainMenu) {
  //       const width = mainMenu.clientWidth;
  //       // 如果宽度小于100px，认为主菜单已折叠
  //       setMainMenuCollapsed(width < 100);
  //     }
  //   };

  //   // 初始检查
  //   checkMainMenuCollapsed();

  //   // 监听变化
  //   const observer = new MutationObserver(checkMainMenuCollapsed);
  //   observer.observe(document.body, { attributes: true, subtree: true, childList: true });

  //   // 清理
  //   return () => observer.disconnect();
  // }, []);

  // 动态样式
  const menuItemStyle = `
    .repo-sidebar-menu .ant-menu-item {
      margin: 4px 12px !important;
      width: calc(100% - 24px) !important;
      border-radius: 6px !important;
      height: 38px !important;
      line-height: 38px !important;
    }
  `;

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: menuItemStyle }} />
      <div style={{
        height: 'calc(100% - 48px)', // 减去top和bottom的高度 (24px + 24px = 48px)
        display: 'flex',
        flexDirection: 'column',
        position: 'absolute',
        top: 24,
        bottom: 24,
        left: 0,
        right: 0,
        background:currentTheme === 'light' ? '#fff' : '#1E1F23',
        border:currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C',
        borderRadius:8,
        boxShadow:'0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        width: collapsed ? '64px' : '200px', // 确保宽度与Sider组件一致
      }}>
        <div
          style={{
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center' ,
            // paddingLeft: collapsed ? '0' : '24px',
            fontWeight: 'bold',
            fontSize: '18px',
            // borderBottom: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C',
            // borderRight: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C', // 移除右边框，避免重复
            // backgroundColor: currentTheme === 'light' ? '#fff' : '#1a1c1e',
            color: currentTheme === 'light' ? '#1E1F23' : '#fff',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
          }}
        >
        {/* 折叠按钮 */}
        <div
          onClick={onCollapse}
          style={{
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            // paddingLeft: 0,
            justifyContent: 'center',
            cursor: 'pointer',
            color: currentTheme === 'light' ? '#535557' : '#C7C8C7',
            backgroundColor: currentTheme === 'light' ? '#fff' : '#1E1F23',
            // borderRight: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C', // 移除右边框，避免重复
            fontSize: '14px',
          }}
        >
          {collapsed ? <MenuUnfoldOutlined style={{ fontSize: '16px' }} /> : <MenuFoldOutlined style={{ fontSize: '16px' }} />}
        </div>
        {/* <ToolOutlined
          style={{ fontSize: collapsed ? '22px' : '20px', marginRight: collapsed ? '0' : '10px' }}
        />
        {!collapsed && 'Workbench'} */}
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', flex: 1, overflow: 'hidden' }}>
        <Menu
          mode="inline"
          items={items}
          defaultOpenKeys={['control', 'development']}
          selectedKeys={[getSelectedMenuKey()]}
          onClick={handleMenuClick}
          style={{
            flex: 1,
            // border: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C', // 移除右边框，避免重复
            overflow: 'auto',
            padding: '8px 0', // 与一级菜单保持一致
          }}
          // 自定义菜单项样式
          className="repo-sidebar-menu"
        />


      </div>
    </div>
    </>
  );
};

export default RepoSidebar;
