import {
  BorderlessTableOutlined,
  CodeOutlined,
  CopyOutlined,
  DownloadOutlined,
  DownOutlined,
  FolderOutlined,
  ForkOutlined,
  InboxOutlined,
  JavaScriptOutlined,
  LockOutlined,
  NotificationOutlined,
  PlusOutlined,
  ReadOutlined,
  SettingOutlined,
  StarOutlined,
  ToolOutlined,
  UnlockOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Avatar,
  Breadcrumb,
  Button,
  Card,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  GetProps,
  Input,
  MenuProps,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Table,
  TableProps,
  Tag,
  Tooltip,
  Upload,
} from 'antd';
import { ReactNode, useEffect, useState } from 'react';
// Import shared components and utilities
import RepoBreadcrumb from '@/components/RepoBreadcrumb';
import { getTheme } from '@/components/RightContent/themeSwitcher';
import {
  createBranch,
  createNewFile,
  deleteFile,
  getRepoBlob,
  getRepoFile,
  getRepoTree,
  listBranch,
  listCommits,
  listProjects,
  updateFile,
} from '@/services/ant-design-pro/gitlab';
import { useLocation, useNavigate } from 'react-router-dom';
import { FileType as BaseFileType } from '../../repository/data';
import { BranchDropdown } from './components';
import { getCurrentTheme } from './utils';

// 扩展 FileType 接口，添加需要的字段
interface FileType extends BaseFileType {
  type?: string; // 文件类型：'tree' 或 'blob'
  id?: string; // 文件ID
  path?: string; // 文件路径
}

const calculateTimeDiff = (dateString: string) => {
  const now = new Date();
  const targetDate = new Date(dateString);
  const diffInHours = Math.abs(now.getTime() - targetDate.getTime()) / (1000 * 60 * 60);

  if (diffInHours >= 24) {
    return `${Math.round(diffInHours / 24)}天前`;
  } else {
    return `${Math.round(diffInHours)}小时前`;
  }
};

export default () => {
  const location = useLocation();
  const navigate = useNavigate();

  // State for current theme
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>(getCurrentTheme());

  // Define interface for project information
  interface ProjectInfo {
    proId?: number;
    defaultBranch?: string;
    proName?: string;
    ownerName?: string;
    update?: string;
    visibility?: string;
    starCount?: number;
    forkCount?: number;
    sshUrl?: string;
    httpUrl?: string;
  }

  // State for project information
  const [projectInfo, setProjectInfo] = useState<ProjectInfo>({});
  // State for branch information
  const [branches, setBranches] = useState<API.BranchInfo[]>([]);
  // State for current commit title
  const [currentCommitTitle, setCurrentCommitTitle] = useState<string>('Initial Commits');
  // State for current commit date
  const [currentCommitDate, setCurrentCommitDate] = useState<string | undefined>(undefined);
  // State for file list
  const [files, setFiles] = useState<FileType[]>([]);
  // State for current path
  const [currentPath, setCurrentPath] = useState<string>('');
  // State for parsed README content
  const [parsedReadmeContent, setParsedReadmeContent] = useState<ParsedMarkdown[]>([]);
  // 不再需要模态框状态
  // State for current file content
  const [currentFileContent, setCurrentFileContent] = useState<string>('');
  // State for current file name
  const [currentFileName, setCurrentFileName] = useState<string>('');
  // State for download dropdown visibility
  const [downloadDropdownVisible, setDownloadDropdownVisible] = useState<boolean>(false);
  // State for available archive formats
  const [archiveFormats] = useState<string[]>(['zip', 'tar', 'tar.gz', 'tar.bz2']);
  // State for selected archive format
  const [selectedFormat, setSelectedFormat] = useState<string>('zip');
  // State for clone dropdown visibility
  const [cloneDropdownVisible, setCloneDropdownVisible] = useState<boolean>(false);

  // State for search term
  const [searchTerm, setSearchTerm] = useState<string>('');
  // State for filtered files (search results)
  const [filteredFiles, setFilteredFiles] = useState<FileType[]>([]);
  // State for upload modal visibility
  const [uploadModalVisible, setUploadModalVisible] = useState<boolean>(false);
  // State for commit message
  const [commitMessage, setCommitMessage] = useState<string>('更新文件');
  // State for selected branch in upload modal
  const [uploadTargetBranch, setUploadTargetBranch] = useState<string>('');
  // State for new directory modal visibility
  const [newDirModalVisible, setNewDirModalVisible] = useState<boolean>(false);
  // 暂存上传文件的内容和信息
  const [uploadFileContent, setUploadFileContent] = useState<string>('');
  const [uploadFileName, setUploadFileName] = useState<string>('');
  // 上传按钮的加载状态
  const [uploadButtonLoading, setUploadButtonLoading] = useState<boolean>(false);
  // 是否处于修改模式（true: 修改现有文件, false: 上传新文件）
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  // State for new directory name
  const [newDirName, setNewDirName] = useState<string>('');
  // State for new directory commit message
  const [newDirCommitMessage, setNewDirCommitMessage] = useState<string>('新增目录');
  // State for new directory target branch
  const [newDirTargetBranch, setNewDirTargetBranch] = useState<string>('');
  // State for new directory merge request checkbox
  const [newDirCreateMergeRequest, setNewDirCreateMergeRequest] = useState<boolean>(false);
  // State for new branch modal visibility
  const [newBranchModalVisible, setNewBranchModalVisible] = useState<boolean>(false);
  // State for new branch name
  const [newBranchName, setNewBranchName] = useState<string>('');
  // State for new branch source
  const [newBranchSource, setNewBranchSource] = useState<string>('');
  // State for delete file modal visibility
  const [deleteFileModalVisible, setDeleteFileModalVisible] = useState<boolean>(false);
  // State for delete file commit message
  const [deleteFileCommitMessage, setDeleteFileCommitMessage] = useState<string>('');
  // State for delete file target branch
  const [deleteFileTargetBranch, setDeleteFileTargetBranch] = useState<string>('');

  // Monitor theme changes
  useEffect(() => {
    // Initialize theme
    setCurrentTheme(getTheme() as 'light' | 'dark');

    // Listen for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          setCurrentTheme(getTheme() as 'light' | 'dark');
        }
      });
    });

    observer.observe(document.body, { attributes: true });

    return () => {
      observer.disconnect();
    };
  }, []);

  // Extract project information from location state or fetch from API
  useEffect(() => {
    const fetchProjectInfo = async () => {
      // Check if location state has project information
      if (location.state && location.state.proId) {
        // 从location.state获取项目信息
        setProjectInfo({
          proId: location.state.proId,
          defaultBranch: location.state.defaultBranch,
          proName: location.state.proName,
          ownerName: location.state.ownerName,
          update: location.state.update,
          visibility: location.state.visibility,
          starCount: location.state.starCount,
          forkCount: location.state.forkCount,
          sshUrl: location.state.sshUrl,
          httpUrl: location.state.httpUrl,
        });
      } else {
        // Fetch projects and use the most recently updated one
        try {
          const projects = await listProjects();
          if (projects && projects.length > 0) {
            // Sort projects by last_activity_at in descending order
            const sortedProjects = [...projects].sort((a, b) => {
              const dateA = new Date(a.last_activity_at || 0);
              const dateB = new Date(b.last_activity_at || 0);
              return dateB.getTime() - dateA.getTime();
            });

            // Get the most recently updated project
            const latestProject = sortedProjects[0];

            setProjectInfo({
              proId: latestProject.id,
              defaultBranch: latestProject.default_branch,
              proName: latestProject.name,
              ownerName: latestProject.owner?.name,
              update: latestProject.last_activity_at,
              visibility: latestProject.visibility,
              starCount: latestProject.star_count,
              forkCount: latestProject.forks_count,
              sshUrl: latestProject.ssh_url_to_repo,
              httpUrl: latestProject.http_url_to_repo,
            });
          }
        } catch (error) {
          console.error('Failed to fetch project information:', error);
        }
      }
    };

    fetchProjectInfo();
  }, [location.state]);

  // 当proId或defaultBranch变化时，获取完整的项目信息
  useEffect(() => {
    const fetchCompleteProjectInfo = async () => {
      if (!projectInfo.proId) return;

      try {
        // 1. 获取项目详细信息（如果需要补充）
        if (!projectInfo.proName || !projectInfo.defaultBranch) {
          const projects = await listProjects();
          const currentProject = projects.find((p) => p.id === projectInfo.proId);

          if (currentProject) {
            // 更新项目信息
            setProjectInfo((prev) => ({
              ...prev,
              defaultBranch: prev.defaultBranch || currentProject.default_branch,
              proName: prev.proName || currentProject.name,
              ownerName: prev.ownerName || currentProject.owner?.name,
              update: prev.update || currentProject.last_activity_at,
              visibility: prev.visibility || currentProject.visibility,
              starCount: prev.starCount || currentProject.star_count,
              forkCount: prev.forkCount || currentProject.forks_count,
              sshUrl: prev.sshUrl || currentProject.ssh_url_to_repo,
              httpUrl: prev.httpUrl || currentProject.http_url_to_repo,
            }));
          }
        }

        // 2. 获取分支信息
        const response = await listBranch({ id: projectInfo.proId });
        setBranches(response);

        // 3. 设置初始提交标题和日期
        // 优先使用当前选择的分支，如果没有则查找默认分支
        const branchToUse = projectInfo.defaultBranch;
        const branchInfo =
          response.find((branch) => branch.name === branchToUse) ||
          response.find((branch) => branch.default === true);

        if (branchInfo?.commit) {
          if (branchInfo.commit.title) {
            setCurrentCommitTitle(branchInfo.commit.title);
          } else {
            setCurrentCommitTitle('Initial Commits');
          }

          if (branchInfo.commit.created_at) {
            setCurrentCommitDate(branchInfo.commit.created_at);
          } else {
            setCurrentCommitDate(undefined);
          }

          // 4. 获取仓库文件树
          fetchRepoTree(projectInfo.proId, branchInfo.name);

          // 5. 获取README.md内容
          fetchReadmeContent(projectInfo.proId, branchInfo.name);
        } else {
          console.warn(`未找到有效的分支信息: ${branchToUse}`);
        }
      } catch (error) {
        console.error('Failed to fetch project information:', error);
      }
    };

    fetchCompleteProjectInfo();
  }, [projectInfo.proId, projectInfo.defaultBranch]);

  // Function to get appropriate icon based on file type and name
  const getFileIcon = (item: API.RepoTree) => {
    // Priority 1: Check if it's a directory
    if (item.type === 'tree') {
      return <FolderOutlined />;
    }
    // Priority 2: Check if it's a JavaScript file
    if (item.name.endsWith('.js')) {
      return <JavaScriptOutlined />;
    }
    // Priority 3: Check if it's a Python file
    if (item.name.endsWith('.py')) {
      return <CodeOutlined />;
    }
    // Priority 4: Check if it's a configuration file
    if (item.name.endsWith('.conf') || item.name.endsWith('.env') || item.name.endsWith('.json')) {
      return <SettingOutlined />;
    }
    // Priority 5: Check if it's README.md
    if (item.name === 'README.md') {
      return <ReadOutlined />;
    }
    // Priority 6: Check if it's a blob (file)
    if (item.type === 'blob') {
      return <BorderlessTableOutlined />;
    }
    // Default: return null if none of the conditions match
    return null;
  };

  // Function to fetch README.md content
  const fetchReadmeContent = async (id?: number, ref?: string) => {
    if (!id || !ref) return;

    try {
      // Ensure all parameters are explicitly set in the request body
      const requestBody = {
        id: id,
        ref: ref,
        file_path: 'README.md',
      };

      const response = await getRepoFile(requestBody);

      // Process the response which is already in UTF-8 format
      if (typeof response === 'string') {
        // Parse markdown for rendering
        setParsedReadmeContent(parseMarkdown(response));
      } else if (response && typeof response === 'object') {
        // If response is an object, try to extract content
        const responseObj = response as any; // Type assertion to avoid TypeScript errors

        if (responseObj.content && typeof responseObj.content === 'string') {
          // Parse markdown for rendering
          setParsedReadmeContent(parseMarkdown(responseObj.content));
        } else {
          const jsonContent = JSON.stringify(responseObj, null, 2); // Format object as string
          setParsedReadmeContent([{ type: 'text', content: jsonContent }]);
        }
      } else {
        const message = 'No content returned from server.';
        setParsedReadmeContent([{ type: 'text', content: message }]);
      }
    } catch (error) {
      console.error('Failed to fetch README.md:', error);
      setParsedReadmeContent([{ type: 'text', content: 'Failed to load README.md content.' }]);
    }
  };

  // Interface for parsed markdown content
  interface ParsedMarkdown {
    type:
      | 'heading1'
      | 'heading2'
      | 'heading3'
      | 'paragraph'
      | 'inlineCode'
      | 'codeBlock'
      | 'text'
      | 'link'
      | 'checkbox';
    content: string;
    language?: string;
    url?: string;
    checked?: boolean;
    children?: ParsedMarkdown[];
  }

  // Helper function to process regex matches and build segments
  const processRegexMatches = (
    text: string,
    regex: RegExp,
    createMatchSegment: (match: RegExpExecArray) => ParsedMarkdown,
  ): ParsedMarkdown[] => {
    const segments: ParsedMarkdown[] = [];
    let match;
    let lastIndex = 0;
    let hasMatches = false;

    while ((match = regex.exec(text)) !== null) {
      hasMatches = true;
      // Add text before the match
      if (match.index > lastIndex) {
        segments.push({
          type: 'text',
          content: text.substring(lastIndex, match.index),
        });
      }

      // Add the matched segment
      segments.push(createMatchSegment(match));

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text after last match
    if (lastIndex < text.length) {
      segments.push({
        type: 'text',
        content: text.substring(lastIndex),
      });
    }

    // If no matches were found, return the original text
    if (!hasMatches) {
      return [{ type: 'text', content: text }];
    }

    return segments;
  };

  // Function to parse markdown text into structured content
  const parseMarkdown = (markdownText: string): ParsedMarkdown[] => {
    const lines = markdownText.split('\n');
    const parsedContent: ParsedMarkdown[] = [];

    let inCodeBlock = false;
    let currentCodeBlock = '';
    let currentCodeLanguage = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Check for code block start/end
      if (line.trim().startsWith('```')) {
        if (!inCodeBlock) {
          // Start of code block
          inCodeBlock = true;
          currentCodeLanguage = line.trim().substring(3).trim() || 'text';
        } else {
          // End of code block
          inCodeBlock = false;
          parsedContent.push({
            type: 'codeBlock',
            content: currentCodeBlock.trim(),
            language: currentCodeLanguage,
          });
          currentCodeBlock = '';
          currentCodeLanguage = '';
        }
        continue;
      }

      if (inCodeBlock) {
        // Add line to current code block
        currentCodeBlock += line + '\n';
        continue;
      }

      // Process headings
      if (line.startsWith('# ')) {
        parsedContent.push({
          type: 'heading1',
          content: line.substring(2).trim(),
        });
      } else if (line.startsWith('## ')) {
        parsedContent.push({
          type: 'heading2',
          content: line.substring(3).trim(),
        });
      } else if (line.startsWith('### ')) {
        parsedContent.push({
          type: 'heading3',
          content: line.substring(4).trim(),
        });
      } else if (line.trim().startsWith('- [ ]') || line.trim().startsWith('- [x]')) {
        // Process checkboxes
        const isChecked = line.trim().startsWith('- [x]');
        const checkboxContent = line
          .trim()
          .substring(isChecked ? 6 : 6)
          .trim();

        // Process the checkbox content for links
        const linkRegex = /\[([^\]]+)\]\(([^\)]+)\)/g;
        const checkboxParts = processRegexMatches(checkboxContent, linkRegex, (match) => ({
          type: 'link',
          content: match[1],
          url: match[2],
        }));

        const hasLinks =
          checkboxParts.length > 1 ||
          checkboxParts[0].type !== 'text' ||
          checkboxParts[0].content !== checkboxContent;

        parsedContent.push({
          type: 'checkbox',
          content: hasLinks ? '' : checkboxContent,
          checked: isChecked,
          children: hasLinks ? checkboxParts : undefined,
        });
      } else {
        // Process links and inline code in regular text
        let text = line;

        // First, process links
        const linkRegex = /\[([^\]]+)\]\(([^\)]+)\)/g;
        const processedText = processRegexMatches(text, linkRegex, (match) => ({
          type: 'link',
          content: match[1],
          url: match[2],
        }));

        // Now process inline code in each text segment
        const finalProcessedText: ParsedMarkdown[] = [];

        for (const segment of processedText) {
          if (segment.type !== 'text') {
            finalProcessedText.push(segment);
            continue;
          }

          const inlineCodeRegex = /`([^`]+)`/g;
          const processedSegments = processRegexMatches(
            segment.content,
            inlineCodeRegex,
            (match) => ({
              type: 'inlineCode',
              content: match[1],
            }),
          );

          // Add all processed segments to the final result
          finalProcessedText.push(...processedSegments);
        }

        // Add all processed segments to the main content
        parsedContent.push(...finalProcessedText);
      }
    }

    return parsedContent;
  };

  // Helper function for rendering headings
  const renderHeading = (content: string, level: 1 | 2 | 3, index: number) => {
    const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;
    return (
      <div key={`md-${index}`}>
        <HeadingTag style={{ marginBottom: 0, marginTop: 24 }}>{content}</HeadingTag>
        <Divider />
      </div>
    );
  };

  // Helper function for rendering code blocks
  const renderCodeBlock = (content: string, index: number, prevItemType?: string) => {
    return (
      <Card
        key={`md-${index}`}
        style={{
          marginBottom: 16,
          background: currentTheme === 'light' ? '#fff' : '#2c2c2c',
          marginTop: prevItemType === 'text' ? 24 : undefined,
        }}
      >
        <div style={{ position: 'relative' }} className="code-block-container">
          <style>
            {`
              .code-block-container .copy-icon {
                display: none;
              }
              .code-block-container:hover .copy-icon {
                display: block;
              }
            `}
          </style>
          <CopyOutlined
            className="copy-icon"
            style={{
              position: 'absolute',
              right: 0,
              top: 0,
              cursor: 'pointer',
              fontSize: '16px',
            }}
            onClick={() => {
              navigator.clipboard.writeText(content);
              message.success('Copied to clipboard');
            }}
          />
          <pre style={{ margin: 0, fontSize: '16px', paddingRight: '24px' }}>{content}</pre>
        </div>
      </Card>
    );
  };

  // Function to render parsed markdown content as React components
  const renderMarkdown = (parsedContent: ParsedMarkdown[]): ReactNode[] => {
    const handleCheckboxChange = () => {
      // This is just a placeholder function since we can't actually modify the README.md file
    };

    return parsedContent.map((item, index) => {
      switch (item.type) {
        case 'heading1':
          return renderHeading(item.content, 1, index);
        case 'heading2':
          return renderHeading(item.content, 2, index);
        case 'heading3':
          return renderHeading(item.content, 3, index);
        case 'inlineCode':
          return (
            <Tag key={`md-${index}`} bordered={false} style={{ fontSize: '16px' }}>
              {item.content}
            </Tag>
          );
        case 'codeBlock':
          return renderCodeBlock(
            item.content,
            index,
            index > 0 ? parsedContent[index - 1].type : undefined,
          );
        case 'link':
          return (
            <a
              key={`md-${index}`}
              href={item.url}
              target="_blank"
              rel="noopener noreferrer"
              style={{ fontSize: '16px' }}
            >
              {item.content}
            </a>
          );
        case 'checkbox':
          if (item.children && item.children.length > 0) {
            return (
              <div
                key={`md-${index}`}
                style={{ display: 'flex', alignItems: 'flex-start', marginBottom: 8 }}
              >
                <Checkbox
                  checked={item.checked}
                  onChange={handleCheckboxChange}
                  style={{ marginRight: 8, marginTop: 3 }}
                />
                <div style={{ fontSize: '16px' }}>{renderMarkdown(item.children)}</div>
              </div>
            );
          }
          return (
            <div
              key={`md-${index}`}
              style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}
            >
              <Checkbox
                checked={item.checked}
                onChange={handleCheckboxChange}
                style={{ marginRight: 8 }}
              />
              <span style={{ fontSize: '16px' }}>{item.content}</span>
            </div>
          );
        case 'text':
        default:
          return item.content ? (
            <span key={`md-${index}`} style={{ fontSize: '16px' }}>
              {item.content}
            </span>
          ) : null;
      }
    });
  };

  // Function to fetch repository tree
  const fetchRepoTree = async (id?: number, ref?: string, path?: string) => {
    if (!id || !ref) return;

    try {
      // 获取文件树
      const treeResponse = await getRepoTree({ id, ref, path });

      // 获取提交信息
      const commitsResponse = await listCommits({ id, ref } as any);

      // 确保提交信息是数组且不为空
      const latestCommit =
        Array.isArray(commitsResponse) && commitsResponse.length > 0 ? commitsResponse[0] : null;

      // 如果有最新提交，更新当前提交标题和日期
      if (latestCommit) {
        setCurrentCommitTitle(latestCommit.title || 'Initial commit');
        setCurrentCommitDate(latestCommit.committed_date);
      }

      // Transform API.RepoTree[] to FileType[]
      const fileList: FileType[] = treeResponse.map((item) => ({
        name: item.name,
        icon: getFileIcon(item),
        lastCommit: latestCommit?.title || currentCommitTitle || 'Initial commit',
        lastUpdate: latestCommit?.committed_date
          ? calculateTimeDiff(latestCommit.committed_date)
          : currentCommitDate
          ? calculateTimeDiff(currentCommitDate)
          : '24 hours ago',
        type: item.type, // 保存文件类型（tree或blob）
        id: item.id, // 保存文件ID
        path: item.path, // 保存文件路径
      }));

      // 如果当前在子目录中，添加返回上一级目录的选项
      if (path && path.length > 0) {
        // 获取上一级目录的路径
        const parentPath = path.includes('/') ? path.substring(0, path.lastIndexOf('/')) : '';

        // 在文件列表的开头添加返回上一级的选项
        fileList.unshift({
          name: '..',
          icon: <FolderOutlined />,
          lastCommit: '',
          lastUpdate: '',
          type: 'tree',
          id: '',
          path: parentPath,
        });
      }

      setFiles(fileList);
    } catch (error) {
      console.error('Failed to fetch repository tree:', error);
    }
  };

  // Function to handle format selection
  const handleFormatChange = (e: any) => {
    setSelectedFormat(e.target.value);
  };

  // Function to download repository archive or files
  const downloadArchive = async () => {
    if (!proId) {
      message.error('项目ID不存在');
      return;
    }

    try {
      message.loading(`正在下载 ${defaultBranch || 'main'} (${selectedFormat})...`);

      // 直接通过浏览器下载（无需处理 Blob）
      const downloadUrl = `/api/v4/projects/${proId}/repository/archive.${selectedFormat}?sha=${
        defaultBranch || 'main'
      }`;

      // 创建隐藏的下载链接
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${proName || 'repo'}-${defaultBranch || 'main'}.${selectedFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success(`成功下载 ${defaultBranch || 'main'} (${selectedFormat})`);

      // 关闭下拉菜单
      setDownloadDropdownVisible(false);
    } catch (error) {
      console.error('下载失败:', error);
      message.error('下载失败，请稍后重试');
    }
  };

  // Reusable component for download format selection
  const DownloadFormatSelector = () => {
    return (
      <div
        style={{
          padding: '12px',
          background: currentTheme === 'light' ? '#fff' : '#1a1c1e',
          color: currentTheme === 'light' ? '#1a1c1e' : '#fff',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
        }}
      >
        <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>选择下载格式</div>
        <Radio.Group onChange={handleFormatChange} value={selectedFormat}>
          {archiveFormats.map((format) => (
            <Radio key={format} value={format} style={{ display: 'block', marginBottom: '8px' }}>
              {format}
            </Radio>
          ))}
        </Radio.Group>
        <Button
          type="primary"
          onClick={downloadArchive}
          style={{ marginTop: '8px', width: '100%' }}
        >
          下载
        </Button>
      </div>
    );
  };

  // Function to handle clone button click
  const handleCloneClick = () => {
    setCloneDropdownVisible(!cloneDropdownVisible);
  };

  // Function to copy text to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        message.success('复制成功');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  };

  // Reusable component for clone URL section
  const CloneUrlSection = ({
    title,
    url,
    urlType,
  }: {
    title: string;
    url?: string;
    urlType: 'ssh' | 'http';
  }) => {
    const fallbackUrl =
      urlType === 'ssh'
        ? `*********************:${ownerName || 'user'}/${proName || 'repository'}.git`
        : `http://devops.hyperc.com/${ownerName || 'user'}/${proName || 'repository'}.git`;

    const displayUrl = url || fallbackUrl;

    return (
      <>
        <h3 style={{ marginBottom: '20px' }}>{title}</h3>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
          <Input value={displayUrl} readOnly style={{ marginRight: '10px', flex: 1 }} />
          <Button icon={<CopyOutlined />} onClick={() => copyToClipboard(displayUrl)} />
        </div>
      </>
    );
  };

  // Reusable component for IDE button
  const IdeButton = ({ name, urlType }: { name: string; urlType: 'ssh' | 'http' }) => {
    const url = urlType === 'ssh' ? projectInfo.sshUrl : projectInfo.httpUrl;
    const fallbackUrl =
      urlType === 'ssh'
        ? `*********************:${ownerName || 'user'}/${proName || 'repository'}.git`
        : `http://devops.hyperc.com/${ownerName || 'user'}/${proName || 'repository'}.git`;

    return (
      <Button
        style={{ textAlign: 'left', height: 'auto', padding: '10px' }}
        onClick={() => copyToClipboard(url || fallbackUrl)}
      >
        {name} ({urlType.toUpperCase()})
      </Button>
    );
  };

  // Function to search files
  const searchFiles = (term: string) => {
    if (!term.trim()) {
      setFilteredFiles([]);
      return;
    }

    // 如果files为空，则搜索defaultFile
    const sourceFiles = files.length > 0 ? files : defaultFile;
    const results = sourceFiles.filter((file) =>
      file.name.toLowerCase().includes(term.toLowerCase()),
    );
    setFilteredFiles(results);
  };

  // 当files或searchTerm变化时更新过滤后的文件列表
  useEffect(() => {
    if (searchTerm) {
      searchFiles(searchTerm);
    } else {
      setFilteredFiles([]);
    }

    // 如果当前正在查看文件内容，则清除搜索条件
    if (currentFileName && currentFileContent && searchTerm) {
      setSearchTerm('');
    }
  }, [files, searchTerm, currentFileName, currentFileContent]);

  // Function to handle file click
  const handleFileClick = async (file: FileType) => {
    if (!proId) {
      message.error('项目ID不存在');
      return;
    }

    if (file.type === 'tree') {
      // 如果是文件夹，获取该文件夹的内容
      try {
        // 更新当前路径
        setCurrentPath(file.path || '');
        // 获取文件夹内容
        await fetchRepoTree(proId, defaultBranch, file.path);
      } catch (error) {
        console.error('获取文件夹内容失败:', error);
        message.error('获取文件夹内容失败');
      }
    } else if (file.type === 'blob') {
      // 如果是文件，获取文件内容
      try {
        if (!file.id) {
          message.error('文件ID不存在');
          return;
        }

        // 获取文件内容
        const blob = await getRepoBlob({ id: proId, sha: file.id });
        if (blob) {
          // 设置当前文件名和内容
          setCurrentFileName(file.name);
          setCurrentFileContent(blob);
        } else {
          message.error('获取文件内容失败');
        }
      } catch (error) {
        console.error('获取文件内容失败:', error);
        message.error('获取文件内容失败');
      }
    }
  };

  // Destructure project information from state
  const { proId, defaultBranch, proName, ownerName, visibility, starCount, forkCount } =
    projectInfo;

  const handleBranchClick = () => {
    navigate('/tools/repo/branch', {
      state: { proId: proId },
    });
  };
  const handleCommitClick = () => {
    navigate('/tools/repo/commits', {
      state: { defaultBranch: defaultBranch, proName: proName, proId: proId },
    });
  };

  // Handle branch selection
  const handleBranchSelect = ({ key }: { key: string }) => {
    // Find the selected branch
    const selectedBranchIndex = parseInt(key.replace('branch-', ''), 10);
    if (!isNaN(selectedBranchIndex) && branches[selectedBranchIndex]) {
      const selectedBranch = branches[selectedBranchIndex];

      // 更新项目信息中的默认分支
      // 注意：这会触发useEffect，因为我们在依赖项中添加了projectInfo.defaultBranch
      setProjectInfo((prev) => ({
        ...prev,
        defaultBranch: selectedBranch.name,
      }));

      // 直接获取新分支的文件和提交信息
      if (projectInfo.proId) {
        fetchRepoTree(projectInfo.proId, selectedBranch.name);
        fetchReadmeContent(projectInfo.proId, selectedBranch.name);
      }
    }
  };

  // Branch items are now handled by the BranchDropdown component
  const fileItems: MenuProps['items'] = [
    {
      key: '1',
      type: 'group',
      label: '当前目录',
      children: [
        {
          key: '1',
          label: '新建文件',
          onClick: () => newFile(proId, defaultBranch, proName, navigate),
        },
        {
          key: '2',
          label: '上传文件',
          onClick: () => setUploadModalVisible(true),
        },
        {
          key: '3',
          label: '新建目录',
          onClick: () => setNewDirModalVisible(true),
        },
      ],
    },
    {
      key: '2',
      type: 'group',
      label: '当前仓库',
      children: [
        {
          key: '4',
          label: '新建分支',
          onClick: () => {
            // 打开新建分支模态框
            setNewBranchModalVisible(true);
            // 设置默认创建来源分支
            setNewBranchSource(defaultBranch || '');
          },
        },
        {
          key: '5',
          label: '新建标签',
          onClick: () =>
            navigate('/tools/repo/newTag', {
              state: {
                proId,
                defaultBranch,
                proName,
              },
            }),
        },
      ],
    },
  ];
  const columns: TableProps<FileType>['columns'] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record: FileType) => (
        <Space>
          {record.icon}
          <a onClick={() => handleFileClick(record)}>{text}</a>
        </Space>
      ),
    },
    {
      title: 'Last Commit',
      dataIndex: 'lastCommit',
      key: 'lastCommit',
    },
    {
      title: 'Last Update',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
    },
  ];
  // Default file if no files are fetched
  const defaultFile: FileType[] = [
    {
      name: 'README.md',
      icon: <ReadOutlined />,
      lastCommit: currentCommitTitle || 'Initial commit',
      lastUpdate: currentCommitDate ? calculateTimeDiff(currentCommitDate) : '24 hours ago',
      type: 'blob',
      id: '',
      path: 'README.md',
    },
  ];

  type SearchProps = GetProps<typeof Input.Search>;
  const { Search } = Input;
  const onSearch: SearchProps['onSearch'] = (value) => {
    searchFiles(value);
  };
  return (
    <>
      <RepoBreadcrumb proName={proName} currentPath={currentPath} />
      <Divider />
      <Row align="middle">
        <Col flex="auto">
          <Row align="middle">
            <h1 style={{ marginRight: 8,marginLeft:10}}>{proName} </h1>
            <h3>{visibility === 'private' ? <LockOutlined /> : <UnlockOutlined />}</h3>
          </Row>
          <span>项目ID: {proId}</span>
        </Col>
        <Col>
          <Space>
            <Button onClick={handleBranchClick} size="large" style={{ marginRight: 6 }}>
              <ToolOutlined />
            </Button>
            <Button onClick={handleCommitClick} size="large" style={{ marginRight: 6 }}>
              <NotificationOutlined />
              <DownOutlined />
            </Button>
            <Button onClick={(e) => e.preventDefault()} size="large" style={{ marginRight: 6 }}>
              <StarOutlined /> {starCount}
            </Button>

            <Button onClick={(e) => e.preventDefault()} size="large" style={{ marginRight: 8 }}>
              <ForkOutlined /> {forkCount}
            </Button>
          </Space>
        </Col>
      </Row>
      <br />
      <Divider />
      <Row justify="space-between" align="middle" style={{ display: 'flex', flexWrap: 'nowrap' }}>
        <Col flex="auto" style={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap' }}>
          <BranchDropdown
            branches={branches}
            defaultBranch={defaultBranch || 'main'}
            onBranchSelect={(key) => handleBranchSelect({ key })}
          />
          <div
            style={{
              fontSize: 16,
              display: 'flex',
              alignItems: 'center',
              marginLeft: 8,
              marginRight: 8,
            }}
          >
            <Breadcrumb separator="/">
              <Breadcrumb.Item>
                <span style={{ whiteSpace: 'nowrap' }}>
                  <a
                    onClick={() => {
                      setCurrentPath('');
                      setCurrentFileName('');
                      setCurrentFileContent('');
                      fetchRepoTree(proId, defaultBranch);
                    }}
                  >
                    {proName}
                  </a>
                </span>
              </Breadcrumb.Item>
              {currentPath &&
                currentPath.split('/').map((segment, index, array) => {
                  // 构建到当前段的路径
                  const pathToSegment = array.slice(0, index + 1).join('/');
                  return (
                    <Breadcrumb.Item key={index}>
                      <span style={{ whiteSpace: 'nowrap' }}>
                        <a
                          onClick={() => {
                            setCurrentPath(pathToSegment);
                            setCurrentFileName('');
                            setCurrentFileContent('');
                            fetchRepoTree(proId, defaultBranch, pathToSegment);
                          }}
                        >
                          {segment}
                        </a>
                      </span>
                    </Breadcrumb.Item>
                  );
                })}
              {currentFileName && (
                <Breadcrumb.Item>
                  <span style={{ whiteSpace: 'nowrap' }}>
                    <a
                      onClick={() => {
                        // 点击当前文件名时，保持当前状态，不做任何操作
                      }}
                    >
                      {currentFileName}
                    </a>
                  </span>
                </Breadcrumb.Item>
              )}
            </Breadcrumb>
          </div>
          <Dropdown
            trigger={['click']}
            menu={{
              items: fileItems,
              style: {
                background: currentTheme === 'light' ? '#fff' : '#1a1c1e',
              },
            }}
          >
            <Button onClick={(e) => e.preventDefault()} size="large">
              <Space>
                <PlusOutlined />
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </Col>
        <Col style={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap' }}>
          <Space>
            <Search
              placeholder="搜索文件"
              onSearch={onSearch}
              style={{ width: 200 }}
              allowClear
              size="large"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              enterButton
            />
            <Button
              icon={<CodeOutlined />}
              size="large"
              style={{ marginRight: 6 }}
            >
              <Space>在IDE中打开</Space>
            </Button>

            <Dropdown
              open={downloadDropdownVisible}
              onOpenChange={setDownloadDropdownVisible}
              dropdownRender={() => <DownloadFormatSelector />}
              trigger={['click']}
            >
              <Button size="large" style={{ marginRight: 8 }}>
                <Space>
                  <DownloadOutlined />
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
            <Dropdown
              open={cloneDropdownVisible}
              onOpenChange={setCloneDropdownVisible}
              dropdownRender={() => (
                <div
                  style={{
                    padding: '20px',
                    background: currentTheme === 'light' ? '#fff' : '#1a1c1e',
                    color: currentTheme === 'light' ? '#1a1c1e' : '#fff',
                    borderRadius: '4px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                    width: '450px',
                  }}
                >
                  <CloneUrlSection title="使用SSH进行克隆" url={projectInfo.sshUrl} urlType="ssh" />
                  <CloneUrlSection
                    title="使用HTTP进行克隆"
                    url={projectInfo.httpUrl}
                    urlType="http"
                  />

                  <h3 style={{ marginBottom: '20px' }}>在 IDE 中打开</h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                    <IdeButton name="Visual Studio Code" urlType="ssh" />
                    <IdeButton name="Visual Studio Code" urlType="http" />
                    <IdeButton name="IntelliJ IDEA" urlType="ssh" />
                    <IdeButton name="IntelliJ IDEA" urlType="http" />
                  </div>
                </div>
              )}
              trigger={['click']}
            >
              <Button onClick={handleCloneClick} size="large" style={{ background: '#87A9FF' }}>
                <Space>
                  克隆
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
          </Space>
        </Col>
      </Row>
      <br />
      <br />
      <Card style={{ padding: '12px' }}>
        <Row align="middle" justify="space-between">
          <Col>
            <Row gutter={16} align="middle">
              <Col>
                <Avatar size="large" style={{ backgroundColor: '#87d068' }}>
                  {projectInfo.proName?.charAt(0).toUpperCase() || '?'}
                </Avatar>
              </Col>
              <Col>
                <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
                  {currentCommitTitle || 'Initial commit'}
                </div>
                <div style={{ color: '#666' }}>
                  {ownerName || 'SuLei'} authored{' '}
                  {currentCommitDate ? calculateTimeDiff(currentCommitDate) : '2 weeks ago'}
                </div>
              </Col>
            </Row>
          </Col>
          <Col>
            <Row gutter={8} align="middle">
              <Col>
                <div
                  style={{ border: '1px solid #d9d9d9', padding: '4px 8px', borderRadius: '4px' }}
                >
                  {branches.find((b) => b.name === projectInfo.defaultBranch)?.commit?.short_id ||
                    '85cc9e42'}
                </div>
              </Col>
              <Col>
                <Button
                  type="text"
                  icon={<CopyOutlined />}
                  style={{ border: '1px solid #d9d9d9' }}
                  onClick={() => {
                    const commitId =
                      branches.find((b) => b.name === projectInfo.defaultBranch)?.commit
                        ?.short_id || '85cc9e42';
                    navigator.clipboard
                      .writeText(commitId)
                      .then(() => message.success(`已复制提交ID: ${commitId}`))
                      .catch((err) => {
                        console.error('复制失败:', err);
                        message.error('复制提交ID失败');
                      });
                  }}
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>
      <br />
      <br />
      {!currentFileName && !currentFileContent && (
        <>
          <Table<FileType>
            columns={columns}
            dataSource={
              searchTerm
                ? filteredFiles.length > 0
                  ? filteredFiles
                  : []
                : files.length > 0
                ? files
                : defaultFile
            }
            pagination={false}
            locale={{
              emptyText:
                searchTerm && filteredFiles.length === 0 ? '没有找到匹配的文件' : '暂无数据',
            }}
          />
          <br />
        </>
      )}

      {currentFileName && currentFileContent ? (
        <>
          <div
            style={{
              border: '1px solid #e8e8e8',
              borderRadius: '4px',
              marginBottom: '20px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: '8px 16px',
                // backgroundColor: '#f5f5f5',
                borderBottom: '1px solid #e8e8e8',
                alignItems: 'center',
              }}
            >
              <div>
                <span style={{ fontWeight: 'bold' }}>{currentFileName}</span>
                {currentFileName.endsWith('.css') && (
                  <span style={{ marginLeft: '8px', color: '#888' }}>
                    {currentFileContent.length} bytes
                  </span>
                )}
              </div>
              <div>
                <Button type="primary" style={{ marginRight: '8px' }}
                onClick={() => {
                // 导航到IDE页面，并传递文件内容和文件名
                navigate('/tools/ide', {
                  state: {
                    fileName: currentFileName,
                    fileContent: currentFileContent,
                    filePath: currentPath ? `${currentPath}/${currentFileName}` : currentFileName,
                  },
                });
              }}>
                  在 IDE 中打开
                </Button>
                <Button
                  style={{ marginRight: '8px' }}
                  onClick={() => {
                    // 设置为修改模式
                    setIsEditMode(true);
                    // 打开上传模态框
                    setUploadModalVisible(true);
                    // 设置默认提交信息
                    setCommitMessage(`更新 ${currentFileName}`);
                    // 设置默认分支
                    setUploadTargetBranch(defaultBranch || '');
                    // 清空上传文件名和内容，等待用户选择新文件
                    setUploadFileName('');
                    setUploadFileContent('');
                  }}
                >
                  修改
                </Button>
                <Button
                  onClick={() => {
                    setDeleteFileModalVisible(true);
                    setDeleteFileCommitMessage(`Delete ${currentFileName}`);
                    setDeleteFileTargetBranch(defaultBranch || '');
                  }}
                >
                  删除
                </Button>
                <Tooltip title="复制文件内容">
                  <Button
                    type="text"
                    icon={<CopyOutlined />}
                    style={{ marginLeft: '8px' }}
                    onClick={() => {
                      navigator.clipboard
                        .writeText(currentFileContent)
                        .then(() => message.success('文件内容已复制到剪贴板'))
                        .catch((err) => {
                          console.error('复制失败:', err);
                          message.error('复制失败');
                        });
                    }}
                  />
                </Tooltip>
                <Tooltip title="下载文件">
                  <Button
                    type="text"
                    icon={<DownloadOutlined />}
                    style={{ marginLeft: '8px' }}
                    onClick={() => {
                      // 根据文件类型设置正确的MIME类型
                      let mimeType = 'text/plain';
                      if (currentFileName.endsWith('.js') || currentFileName.endsWith('.jsx')) {
                        mimeType = 'application/javascript';
                      } else if (currentFileName.endsWith('.css')) {
                        mimeType = 'text/css';
                      } else if (currentFileName.endsWith('.html')) {
                        mimeType = 'text/html';
                      } else if (currentFileName.endsWith('.json')) {
                        mimeType = 'application/json';
                      } else if (currentFileName.endsWith('.md')) {
                        mimeType = 'text/markdown';
                      } else if (currentFileName.endsWith('.py')) {
                        mimeType = 'text/x-python';
                      } else if (currentFileName.endsWith('.java')) {
                        mimeType = 'text/x-java';
                      }

                      const blob = new Blob([currentFileContent], { type: mimeType });
                      const url = URL.createObjectURL(blob);
                      const link = document.createElement('a');
                      link.href = url;
                      link.download = currentFileName;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                      URL.revokeObjectURL(url);
                    }}
                  />
                </Tooltip>
              </div>
            </div>
            <div
              style={{
                display: 'flex',
                // backgroundColor: '#fff',
                fontFamily: 'monospace',
              }}
            >
              <div
                style={{
                  width: '50px',
                  // backgroundColor: '#f5f5f5',
                  color: '#888',
                  textAlign: 'right',
                  padding: '0',
                  userSelect: 'none',
                }}
              >
                {currentFileContent.split('\n').map((_, i) => (
                  <div
                    key={i}
                    style={{
                      padding: '0 8px',
                      lineHeight: '1.5',
                      height: '21px',
                      fontSize: '14px',
                    }}
                  >
                    {i + 1}
                  </div>
                ))}
              </div>
              <pre
                style={{
                  margin: 0,
                  padding: '0 16px',
                  overflow: 'auto',
                  flex: 1,
                  fontSize: '14px',
                  lineHeight: '1.5',
                }}
              >
                {currentFileContent}
              </pre>
            </div>
          </div>
          <br />
        </>
      ) : (
        // 只在根目录（currentPath为空）时显示README.md
        !currentPath && (
          <Card title="README.md" style={{ marginTop: '20px' }}>
            <div style={{ whiteSpace: 'pre-wrap', fontSize: '16px' }}>
              {renderMarkdown(parsedReadmeContent)}
            </div>
          </Card>
        )
      )}

      {/* Upload/Edit File Modal */}
      <Modal
        title={isEditMode ? `修改文件: ${currentFileName}` : '上传文件'}
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          setIsEditMode(false);
          setUploadFileName('');
          setUploadFileContent('');
        }}
        footer={null}
        width={700}
      >
        <div style={{ marginBottom: 16 }}>
          {!uploadFileName ? (
            <Upload.Dragger
              name="file"
              multiple={false}
              showUploadList={false}
              customRequest={({ file, onSuccess, onError, onProgress }) => {
                try {
                  // 显示上传进度
                  onProgress?.({ percent: 50 });

                  // 读取文件内容
                  const reader = new FileReader();
                  reader.readAsText(file as Blob);

                  reader.onload = (e) => {
                    try {
                      const content = e.target?.result as string;
                      const fileName = (file as any).name;
                      const fileSize = (file as File).size;

                      // 暂存文件内容和文件名
                      setUploadFileContent(content);
                      setUploadFileName(fileName);

                      // 设置默认提交信息
                      if (currentFileName) {
                        setCommitMessage(`更新 ${currentFileName}`);
                      } else {
                        setCommitMessage(`添加文件 ${fileName}`);
                      }

                      // 标记为上传成功（仅UI显示，实际上传在点击按钮后进行）
                      onSuccess?.({ fileName, fileSize }, new XMLHttpRequest());
                      message.success(`${fileName} 已上传`);
                    } catch (error) {
                      console.error('文件读取失败:', error);
                      onError?.(error as any);
                      message.error(`文件读取失败: ${(error as any)?.message || '未知错误'}`);
                    }
                  };

                  reader.onerror = (error) => {
                    console.error('读取文件失败:', error);
                    onError?.(error as any);
                    message.error('读取文件失败');
                  };
                } catch (error) {
                  console.error('文件处理失败:', error);
                  onError?.(error as any);
                  message.error(`文件处理失败: ${(error as any)?.message || '未知错误'}`);
                }
              }}
              beforeUpload={(file) => {
                // 文件大小限制 (10MB)
                const isLt10M = file.size / 1024 / 1024 < 10;
                if (!isLt10M) {
                  message.error('文件大小不能超过 10MB!');
                  return Upload.LIST_IGNORE;
                }
                return true;
              }}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">
                拖拽或<a>上传</a>文件
              </p>
            </Upload.Dragger>
          ) : (
            <div
              style={{
                border: '1px dashed #d9d9d9',
                borderRadius: '2px',
                padding: '20px',
                textAlign: 'center',
                background: '#fafafa',
                minHeight: '180px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <img
                src={
                  uploadFileName.endsWith('.png') ||
                  uploadFileName.endsWith('.jpg') ||
                  uploadFileName.endsWith('.jpeg') ||
                  uploadFileName.endsWith('.gif')
                    ? 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABmJLR0QA/wD/AP+gvaeTAAAA70lEQVRIie2UMQrCQBBFn1FsxCNoYSd4Ao/gMcQ72FtZegPxCDYWXkA9QAqxsBSx0VhEIZuZZLMbm4DggyHDzp/9f3YmkPFXqAIjoA/UXZ0CfWAIVJIYl4AesACegK5RtwNmwB2YAM04xnngCuwNY4sXcAHySQzAGN8M44gE5qGGc+AQYRxwBFpxxkUgCHkbxgFzoJzUHKAGXELGb6DhqW0B99C9a1/jgDJwDhlPPTUNYBu6/4gzBihFmK891tQwzOOMA2rALWJ7Bp7qGvAIaUfAMsL8O/Yh7RdQiTBfGvUdoJ3EOGMH1IAWUEpjmJHxAW8LQbAm5xY8AAAAAElFTkSuQmCC'
                    : 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABmJLR0QA/wD/AP+gvaeTAAAA0ElEQVRIie3TMQrCQBDG8Z8RCx/Axs7GB7C1sbfxBQRPYO0VvIKVYGlhYeUNfAMLwcrOB7CQmMxuspvGwj8MZGdn5tuQDfzxbRbAGTgBs5TJc+AAPICdUZ8CB2APTGKN+8AZeBrGFnfgAgyijAFKYAvcDOOAK7CINe4BFbAOGAesgW5Kc4AusAoYb4FWSvOAEbAMGK+Adkrz5/MXwDxgvKTGPGAKHAPGR2CSw7wBFMANuACtHOYBTeAC1Dn/Ug1gDrSBZg7jP3LAEKhomJf/5n/8Bq/ZQRj7W+CbAAAAAElFTkSuQmCC'
                }
                alt="文件图标"
                style={{ width: '24px', height: '24px', marginBottom: '8px' }}
              />
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '4px' }}>
                {uploadFileContent.length} bytes
              </div>
              <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>
                {uploadFileName}
              </div>
              <a
                onClick={() => {
                  setUploadFileContent('');
                  setUploadFileName('');
                }}
                style={{ color: '#1890ff', cursor: 'pointer' }}
              >
                移除文件
              </a>
            </div>
          )}
        </div>

        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>提交信息</div>
          <Input
            value={commitMessage}
            onChange={(e) => setCommitMessage(e.target.value)}
            placeholder={currentFileName ? `更新 ${currentFileName}` : '更新文件'}
          />
        </div>

        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>目标分支</div>
          <Select
            style={{ width: '100%' }}
            value={uploadTargetBranch || defaultBranch}
            onChange={(value) => setUploadTargetBranch(value)}
            options={branches.map((branch) => ({
              value: branch.name,
              label: branch.name,
            }))}
          />
        </div>

        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button style={{ marginRight: 8 }} onClick={() => setUploadModalVisible(false)}>
            取消
          </Button>
          <Button
            type="primary"
            icon={<UploadOutlined />}
            loading={uploadButtonLoading}
            onClick={async () => {
              // 检查是否有文件内容和文件名
              if (!uploadFileContent || !uploadFileName) {
                message.error('请先选择文件');
                return;
              }

              // 检查 proId 是否存在
              if (!proId) {
                message.error('项目ID不存在');
                return;
              }

              try {
                setUploadButtonLoading(true);

                if (isEditMode) {
                  // 修改模式：使用原始文件名作为 file_path
                  const filePath = currentPath
                    ? `${currentPath}/${currentFileName}`
                    : currentFileName;

                  // 调用 updateFile API 更新文件
                  await updateFile({
                    id: proId,
                    file_path: filePath,
                    branch: uploadTargetBranch || defaultBranch || 'main',
                    content: uploadFileContent,
                    commit_message: commitMessage || `更新文件 ${currentFileName}`,
                  });

                  message.success(`${currentFileName} 更新成功`);
                } else {
                  // 上传新文件模式：使用上传的文件名作为 file_path
                  const filePath = currentPath
                    ? `${currentPath}/${uploadFileName}`
                    : uploadFileName;

                  // 调用 createNewFile API 创建新文件
                  await createNewFile({
                    id: proId,
                    file_path: filePath,
                    branch: uploadTargetBranch || defaultBranch || 'main',
                    content: uploadFileContent,
                    commit_message: commitMessage || `添加文件 ${uploadFileName}`,
                  });

                  message.success(`${uploadFileName} 上传成功`);
                }

                // 关闭模态框
                setUploadModalVisible(false);

                // 清空暂存的文件内容和文件名
                setUploadFileContent('');
                setUploadFileName('');
                // 重置编辑模式
                setIsEditMode(false);

                // 刷新文件列表
                fetchRepoTree(proId, uploadTargetBranch || defaultBranch || 'main', currentPath);

                // 如果当前正在查看文件内容，并且是在编辑模式下，则刷新文件内容
                if (isEditMode && currentFileName) {
                  // 获取文件内容
                  const filePath = currentPath
                    ? `${currentPath}/${currentFileName}`
                    : currentFileName;
                  const fileContent = await getRepoFile({
                    id: proId,
                    ref: uploadTargetBranch || defaultBranch || 'main',
                    file_path: filePath,
                  });

                  if (fileContent) {
                    setCurrentFileContent(fileContent);
                  }
                }
              } catch (error) {
                console.error('文件操作失败:', error);
                message.error(
                  `文件${isEditMode ? '更新' : '上传'}失败: ${
                    (error as any)?.message || '未知错误'
                  }`,
                );
              } finally {
                setUploadButtonLoading(false);
              }
            }}
          >
            {isEditMode ? '更新文件' : '上传文件'}
          </Button>
        </div>
      </Modal>

      {/* New Directory Modal */}
      <Modal
        title="创建目录"
        open={newDirModalVisible}
        onCancel={() => setNewDirModalVisible(false)}
        footer={null}
        width={700}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>目录名称</div>
          <Input
            value={newDirName}
            onChange={(e) => setNewDirName(e.target.value)}
            placeholder="输入目录名称"
          />
        </div>

        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>提交信息</div>
          <Input.TextArea
            value={newDirCommitMessage}
            onChange={(e) => setNewDirCommitMessage(e.target.value)}
            placeholder="新增目录"
            rows={4}
          />
        </div>

        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>目标分支</div>
          <Select
            style={{ width: '100%' }}
            value={newDirTargetBranch || defaultBranch}
            onChange={(value) => setNewDirTargetBranch(value)}
            options={branches.map((branch) => ({
              value: branch.name,
              label: branch.name,
            }))}
          />
        </div>

        <div style={{ marginBottom: 16 }}>
          <Checkbox
            checked={newDirCreateMergeRequest}
            onChange={(e) => setNewDirCreateMergeRequest(e.target.checked)}
          >
            使用这些更改创建新的合并请求
          </Checkbox>
        </div>

        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button style={{ marginRight: 8 }} onClick={() => setNewDirModalVisible(false)}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={() => {
              if (!newDirName) {
                message.error('请输入目录名称');
                return;
              }
              // 这里应该调用API来创建目录
              message.success('目录创建成功');
              setNewDirModalVisible(false);
              // 刷新文件列表
              fetchRepoTree(proId, newDirTargetBranch || defaultBranch || 'main');
            }}
          >
            创建目录
          </Button>
        </div>
      </Modal>

      {/* New Branch Modal */}
      <Modal
        title="新建分支"
        open={newBranchModalVisible}
        onCancel={() => {
          setNewBranchModalVisible(false);
          // 清空分支名称
          setNewBranchName('');
        }}
        footer={null}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>分支名称</div>
          <Input
            value={newBranchName}
            onChange={(e) => setNewBranchName(e.target.value)}
            placeholder="输入分支名称"
          />
        </div>

        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>创建自</div>
          <Select
            style={{ width: '100%' }}
            value={newBranchSource || defaultBranch}
            onChange={(value) => setNewBranchSource(value)}
            options={branches.map((branch) => ({
              value: branch.name,
              label: branch.name,
            }))}
          />
        </div>

        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            style={{ marginRight: 8 }}
            onClick={() => {
              setNewBranchModalVisible(false);
              // 清空分支名称
              setNewBranchName('');
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            onClick={async () => {
              if (!newBranchName) {
                message.error('请输入分支名称');
                return;
              }

              if (!newBranchSource) {
                message.error('请选择创建来源分支');
                return;
              }

              if (!proId) {
                message.error('项目ID不存在');
                return;
              }

              try {
                // 调用 createBranch API 创建分支
                await createBranch({
                  id: proId,
                  name: newBranchName,
                  ref: newBranchSource,
                });

                message.success('分支创建成功');
                setNewBranchModalVisible(false);

                // 清空表单
                setNewBranchName('');

                // 刷新分支列表
                const branchResponse = await listBranch({ id: proId });
                setBranches(branchResponse);
              } catch (error) {
                console.error('创建分支失败:', error);
                message.error(`创建分支失败: ${(error as any)?.message || '未知错误'}`);
              }
            }}
          >
            创建分支
          </Button>
        </div>
      </Modal>

      {/* Delete File Modal */}
      <Modal
        title={`删除 ${currentFileName}`}
        open={deleteFileModalVisible}
        onCancel={() => setDeleteFileModalVisible(false)}
        footer={null}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>提交信息</div>
          <Input.TextArea
            value={deleteFileCommitMessage}
            onChange={(e) => setDeleteFileCommitMessage(e.target.value)}
            placeholder={`删除文件 ${currentFileName}`}
            rows={4}
          />
        </div>

        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>目标分支</div>
          <Input value={deleteFileTargetBranch} disabled style={{ width: '100%' }} />
        </div>

        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button style={{ marginRight: 8 }} onClick={() => setDeleteFileModalVisible(false)}>
            取消
          </Button>
          <Button
            type="primary"
            danger
            onClick={async () => {
              if (!proId) {
                message.error('项目ID不存在');
                return;
              }

              if (!deleteFileTargetBranch) {
                message.error('目标分支不能为空');
                return;
              }

              if (!currentFileName) {
                message.error('文件名不能为空');
                return;
              }

              try {
                // 构建文件路径
                const filePath = currentPath
                  ? `${currentPath}/${currentFileName}`
                  : currentFileName;

                // 调用 deleteFile API 删除文件
                await deleteFile({
                  id: proId,
                  file_path: filePath,
                  branch: deleteFileTargetBranch,
                  commit_message: deleteFileCommitMessage || `删除文件 ${currentFileName}`,
                });

                message.success(`文件 ${currentFileName} 已删除`);
                setDeleteFileModalVisible(false);

                // 返回到文件夹视图
                setCurrentFileName('');
                setCurrentFileContent('');
                fetchRepoTree(proId, defaultBranch, currentPath);
              } catch (error) {
                console.error('删除文件失败:', error);
                message.error(`删除文件失败: ${(error as any)?.message || '未知错误'}`);
              }
            }}
          >
            删除文件
          </Button>
        </div>
      </Modal>
      </>
  );
};
// 新建文件函数
const newFile = (proId?: number, defaultBranch?: string, proName?: string, navigateFunc?: any) => {
  if (navigateFunc) {
    // 导航到新建文件页面，并传递项目信息
    navigateFunc('/tools/repo/files/newFile', {
      state: {
        proId,
        defaultBranch,
        proName,
      },
    });
  }
};

export { newFile };
