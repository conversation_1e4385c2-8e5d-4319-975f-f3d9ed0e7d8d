const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const session = require('express-session');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
require('dotenv').config();

// 设置时区为 Asia/Shanghai
process.env.TZ = 'Asia/Shanghai';

// 在开发环境中忽略自签名证书错误
if (process.env.NODE_ENV === 'development') {
  process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
  console.log('⚠️  开发环境：已禁用SSL证书验证');
}

const keycloakConfig = require('./config/keycloak');
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/user');
const protectedRoutes = require('./routes/protected');
const userManagementRoutes = require('./routes/userManagement');
const gitRoutes = require("./routes/gitRoutes");
const deepseekRoutes = require("./routes/deepseekRoutes");
const deployNodeRoutes = require("./routes/deployNodeRoutes");
const { errorHandler } = require('./middleware/errorHandler');

const app = express();
const WEB_SERVER_PORT = process.env.WEB_SERVER_PORT || 8001;

// 中间件配置
app.use(helmet());
app.use(morgan((tokens, req, res) => {
  const date = new Date();
  return [
    tokens['remote-addr'](req, res),
    '-',
    tokens['remote-user'](req, res),
    `[${date.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }).replace(',', '')}]`,
    `"${tokens.method(req, res)}`,
    tokens.url(req, res),
    `HTTP/${tokens['http-version'](req, res)}"`,
    tokens.status(req, res),
    tokens.res(req, res, 'content-length'),
    `"${tokens.referrer(req, res)}"`,
    `"${tokens['user-agent'](req, res)}"`
  ].join(' ');
}));
app.use(cookieParser());

// CORS配置
app.use(cors({
  origin: process.env.FRONTEND_URL,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Body parser配置
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Session配置
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-session-secret',
  resave: false,
  saveUninitialized: true,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24小时
  }
}));

// Keycloak配置
const keycloak = keycloakConfig.initKeycloak(app);

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'keycloak-backend'
  });
});

// 路由配置
app.use(express.json());
app.use('/api/auth', authRoutes);
app.use('/api/user', userRoutes);
app.use('/api/admin', userManagementRoutes);
app.use('/api/protected', keycloak.protect(), protectedRoutes);
app.use("/api/devops", keycloak.protect(), gitRoutes);
app.use("/api/deploy", deepseekRoutes); // Ensure this line is not duplicated
app.use("/api/node", deployNodeRoutes);

// 错误处理中间件
app.use(errorHandler);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl
  });
});

//socket io server
const socketServer = require("http").createServer(app);
const SOCKET_PORT = process.env.WEB_SERVER_SOCKET_PORT || 8002;
const io = require('socket.io')(socketServer, {
    cors: {
        origin: '*'
    }});

let data = []; // Define data at the top level

io.on('connection', (socket) => {
  // console.log('A user connected');
  // socket.broadcast.emit('new data', data);
  socket.on('client-event', (msg) => {
    console.log('Message from client: ' + msg);
    io.emit('new data', msg);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected');
  });
});
//推送消息
app.put("/api/ocd/receive", (req, res) => {
  try {
    console.log("Received a new data");
    const jsonData = JSON.stringify(req.body); // Convert req.body to JSON format
    data.push(jsonData);
    console.log(jsonData);
    io.emit('new data', jsonData); // Send the data to all connected clients
    res.status(200).json({ error_code: 0, error_message: "" });
  } catch (error) {
    console.error("Error processing data:", error);
    res.status(500).json({ error_code: -1, error_message: "server error" });
  }
});

// 启动服务器
app.listen(WEB_SERVER_PORT, () => {
  console.log(`🚀 Server is running on port ${WEB_SERVER_PORT}`);
  console.log(`📍 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔐 Keycloak URL: ${process.env.KEYCLOAK_URL}`);
  // console.log(`🌐 Frontend URL: ${process.env.FRONTEND_URL}`);
});
socketServer.listen(SOCKET_PORT, () => {
  console.log(`🌐 Socket server running on port ${SOCKET_PORT}`);
});

module.exports = app;
