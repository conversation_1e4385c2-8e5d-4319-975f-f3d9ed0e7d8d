// Layout for repository pages
import RepoSidebar from '@/components/RepoSidebar';

import { Layout } from 'antd';
import { useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';

const { Sider, Content } = Layout;

export default () => {
  const [collapsed, setCollapsed] = useState(false);


  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  const [mainMenuCollapsed, setMainMenuCollapsed] = useState(false);



  // 检测主菜单的折叠状态
  useEffect(() => {
    const checkMainMenuCollapsed = () => {
      // 获取主菜单宽度，判断是否折叠
      const mainMenu = document.querySelector('.ant-pro-sider');
      if (mainMenu) {
        const width = mainMenu.clientWidth;
        // 如果宽度小于100px，认为主菜单已折叠
        setMainMenuCollapsed(width < 100);
      }
    };

    // 初始检查
    checkMainMenuCollapsed();

    // 监听变化
    const observer = new MutationObserver(checkMainMenuCollapsed);
    observer.observe(document.body, { attributes: true, subtree: true, childList: true });

    // 清理
    return () => observer.disconnect();
  }, []);
  return (
    <Layout>
      <Sider
        width={200}
        collapsedWidth={64}
        trigger={null} // 移除默认的触发器
        collapsed={collapsed}
        className="fixed-sidebar repo-sider"
        style={{
          backgroundColor: 'transparent', // 使用透明背景，避免在margin区域显示背景色
        }}
      >
        <style>
          {`
    .fixed-sidebar {
      position: fixed !important;
      left: ${mainMenuCollapsed ? '64px' : '285px'} !important;
      top: 0;
      bottom: 0;
      height: 100vh;
      z-index: 90 !important;
    }
  `}
        </style>
        <RepoSidebar collapsed={collapsed} onCollapse={toggleCollapsed} />
      </Sider>
      <Layout
        style={{
          marginLeft: mainMenuCollapsed
            ? collapsed
              ? '70px'
              : '200px' // 主菜单折叠时
            : collapsed
            ? '70px'
            : '200px', // 主菜单展开时
        }}
      >
        <Content>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};
