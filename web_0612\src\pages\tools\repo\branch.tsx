import { createBranch, listBranch, listProjects, removeBranch, removeMergedBranch } from '@/services/ant-design-pro/gitlab';
import { DeleteOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Button, Divider, Form, Input, List, Modal, Space, Tabs, message } from 'antd';
import React, { useEffect, useState, useMemo } from 'react';

import { useLocation, useNavigate } from 'react-router-dom';

import "./branch.less";
import RepoBreadcrumb from '@/components/RepoBreadcrumb';

const { TabPane } = Tabs;

// Import shared components, utilities and hooks
import { BranchList, transformBranchData } from './components';
import { isActiveBranch } from './utils';
export default () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [projectId, setProjectId] = useState<number | undefined>(undefined);
    const [activeTab, setActiveTab] = useState('tab1');
    const [branches, setBranches] = useState<API.BranchInfo[]>([]);
    const [filteredBranch, setFilteredBranch] = useState<API.BranchInfo[]>([]);

    // 获取项目ID，如果location没有携带信息，则从最近更新的项目中获取
    useEffect(() => {
        const fetchProjectId = async () => {
            // 检查location是否携带proId
            if (location.state && location.state.proId) {
                setProjectId(location.state.proId);
            } else {
                // 如果没有，从最近更新的项目中获取
                try {
                    const projects = await listProjects();
                    if (projects && projects.length > 0) {
                        // 按最后活动时间排序（降序）
                        const sortedProjects = [...projects].sort((a, b) => {
                            const dateA = new Date(a.last_activity_at || 0);
                            const dateB = new Date(b.last_activity_at || 0);
                            return dateB.getTime() - dateA.getTime();
                        });

                        // 获取最近更新的项目
                        const latestProject = sortedProjects[0];
                        setProjectId(latestProject.id);
                    }
                } catch (error) {
                    console.error('Failed to fetch project information:', error);
                }
            }
        };

        fetchProjectId();
    }, [location.state]);

    // 删除分支相关状态
    const [deleteModalVisible, setDeleteModalVisible] = useState(false);
    const [branchToDelete, setBranchToDelete] = useState<string>('');

    // 删除已合并分支相关状态
    const [deleteMergedModalVisible, setDeleteMergedModalVisible] = useState(false);

    // 新建分支相关状态
    const [createModalVisible, setCreateModalVisible] = useState(false);
    const [form] = Form.useForm();
    useEffect(() => {
        const fetchBranch = async () => {
            if (!projectId) return;

            const sendData = { id: projectId };
            try {
                const response = await listBranch(sendData);
                setBranches(response);
                setFilteredBranch(response);
            } catch (error) {
                console.error('Failed to fetch branch:', error);
            }
        };

        fetchBranch();
    }, [projectId]);

    // 创建分支加载状态
    const [createBranchLoading, setCreateBranchLoading] = useState(false);

    // 处理新建分支
    const handleCreateBranch = async (values: { name: string; ref: string }) => {
        if (!projectId) {
            message.error('项目ID不存在');
            return;
        }

        if (!values.name) {
            message.error('请输入分支名称');
            return;
        }

        if (!values.ref) {
            message.error('请输入创建来源');
            return;
        }

        try {
            setCreateBranchLoading(true);

            // 调用 createBranch API 创建分支
            await createBranch({
                id: projectId,
                name: values.name,
                ref: values.ref
            });

            message.success('分支创建成功');
            setCreateModalVisible(false);
            form.resetFields();

            // 刷新分支列表
            const branchResponse = await listBranch({ id: projectId });
            setBranches(branchResponse);
            setFilteredBranch(branchResponse);
        } catch (error) {
            console.error('创建分支失败:', error);
            message.error(`创建分支失败: ${(error as any)?.message || '未知错误'}`);
        } finally {
            setCreateBranchLoading(false);
        }
    };

    // 处理删除分支
    const showDeleteConfirm = (branchName: string) => {
        // 检查是否是受保护分支
        const branch = filteredBranch.find(b => b.name === branchName);
        if (branch?.protected) {
            message.warning('受保护分支不能删除');
            return;
        }

        setBranchToDelete(branchName);
        setDeleteModalVisible(true);
    };

    const handleDeleteBranch = async () => {
        if (!projectId) {
            message.error('项目ID不存在');
            return;
        }

        try {
            await removeBranch({
                id: projectId,
                name: branchToDelete
            });
            message.success('分支删除成功');
            setDeleteModalVisible(false);

            // 刷新分支列表
            const branchResponse = await listBranch({ id: projectId });
            setBranches(branchResponse);
            setFilteredBranch(branchResponse);
        } catch (error) {
            message.error('删除分支失败');
            console.error('Failed to delete branch:', error);
        }
    };

    // 处理删除已合并分支
    const handleDeleteMergedBranches = async () => {
        if (!projectId) {
            message.error('项目ID不存在');
            return;
        }

        try {
            await removeMergedBranch({
                id: projectId
            });
            message.success('已合并分支删除成功');
            setDeleteMergedModalVisible(false);

            // 刷新分支列表
            const branchResponse = await listBranch({ id: projectId });
            setBranches(branchResponse);
            setFilteredBranch(branchResponse);
        } catch (error) {
            message.error('删除已合并分支失败');
            console.error('Failed to delete merged branches:', error);
        }
    };

    // 搜索功能
    const handleSearch = (value: string) => {
        if (value) {
            const filtered = branches.filter(branch =>
                branch.name.toLowerCase().includes(value.toLowerCase())
            );
            setFilteredBranch(filtered);
        } else {
            setFilteredBranch(branches);
        }
    };

    // 复制分支名称
    const copyBranchName = (branchName: string) => {
        navigator.clipboard.writeText(branchName)
            .then(() => {
                message.success(`已复制分支名称: ${branchName}`);
            })
            .catch(err => {
                console.error('Failed to copy branch name:', err);
                message.error('复制失败');
            });
    };

    // 导航到分支的files页面
    const navigateToBranchFiles = async (branchName: string) => {
        // 查找分支信息
        const branch = filteredBranch.find(b => b.name === branchName);
        if (!branch) {
            message.error(`找不到分支: ${branchName}`);
            return;
        }

        // 获取更完整的项目信息（如果需要）
        let proName = location.state?.proName;
        let ownerName = location.state?.ownerName;
        let visibility = location.state?.visibility;
        let starCount = location.state?.starCount;
        let forkCount = location.state?.forkCount;
        let sshUrl = location.state?.sshUrl;
        let httpUrl = location.state?.httpUrl;

        // 如果缺少关键信息，尝试从项目列表中获取
        if (!proName || !ownerName || !visibility) {
            try {
                const projects = await listProjects();
                const project = projects.find(p => p.id === projectId);
                if (project) {
                    proName = proName || project.name;
                    ownerName = ownerName || project.owner?.name;
                    visibility = visibility || project.visibility;
                    starCount = starCount || project.star_count;
                    forkCount = forkCount || project.forks_count;
                    sshUrl = sshUrl || project.ssh_url_to_repo;
                    httpUrl = httpUrl || project.http_url_to_repo;
                }
            } catch (error) {
                console.error('Failed to fetch project details:', error);
            }
        }

        // 导航到files页面，并传递必要的参数
        console.log(`将跳转到files页面，并使用分支: ${branchName}`);
        navigate('/tools/repo/files', {
            state: {
                proId: projectId,
                defaultBranch: branchName, // 设置选中的分支为默认分支
                proName: proName || '项目',
                ownerName: ownerName || '用户',
                update: branch.commit?.created_at,
                visibility: visibility || 'private',
                starCount: starCount || 0,
                forkCount: forkCount || 0,
                sshUrl: sshUrl,
                httpUrl: httpUrl
            },
            replace: true // 使用replace而不是push，确保完全刷新状态
        });
    };

    // 处理比较分支
    const handleCompare = (branchName: string) => {
        // 查找默认分支
        const defaultBranch = filteredBranch.find(b => b.default === true);
        const targetBranch = defaultBranch ? defaultBranch.name : 'main';

        // 导航到比较结果页面
        navigate('/tools/repo/compare/compare_result', {
            state: {
                sourceProjectId: projectId,
                targetProjectId: projectId,
                sourceProject: location.state?.proName || '项目',
                targetProject: location.state?.proName || '项目',
                sourceBranch: branchName,
                targetBranch: targetBranch
            }
        });
    };

    // 处理合并请求
    const handleMergeRequest = (branchName: string) => {
        // 查找默认分支
        const defaultBranch = filteredBranch.find(b => b.default === true);
        const targetBranch = defaultBranch ? defaultBranch.name : 'main';

        // 导航到新建合并请求页面
        navigate('/tools/repo/newMergeRequest', {
            state: {
                proId: projectId,
                proName: location.state?.proName || '项目',
                sourceBranch: branchName,
                targetBranch: targetBranch,
                check: 'mergeRequests'
            }
        });
    };


  // 根据更新时间分类分支
  const { activeBranches, inactiveBranches } = useMemo(() => {
    const active = filteredBranch.filter(branch => isActiveBranch(branch.commit?.created_at));
    const inactive = filteredBranch.filter(branch => !isActiveBranch(branch.commit?.created_at));

    return {
      activeBranches: active.map(transformBranchData),
      inactiveBranches: inactive.map(transformBranchData)
    };
  }, [filteredBranch]);

  // 概览页面显示的活跃分支（最多5个）
  const activePreviewBranches = useMemo(() => activeBranches.slice(0, 5), [activeBranches]);

  // 概览页面显示的非活跃分支（最多5个）
  const inactivePreviewBranches = useMemo(() => inactiveBranches.slice(0, 5), [inactiveBranches]);

  return (
    <>
      {/* 删除分支确认框 */}
      <Modal
        title="删除分支"
        open={deleteModalVisible}
        onOk={handleDeleteBranch}
        onCancel={() => setDeleteModalVisible(false)}
        okText="确认删除"
        cancelText="取消"
      >
        <p>确定要删除分支 "{branchToDelete}" 吗？</p>
        <p>此操作不可恢复。</p>
      </Modal>

      {/* 删除已合并分支确认框 */}
      <Modal
        title="删除已合并分支"
        open={deleteMergedModalVisible}
        onOk={handleDeleteMergedBranches}
        onCancel={() => setDeleteMergedModalVisible(false)}
        okText="确认删除"
        cancelText="取消"
      >
        <p>确定要删除所有已合并分支吗？</p>
        <p>此操作将删除所有已合并到默认分支的分支，且不可恢复。</p>
      </Modal>

      {/* 新建分支模态框 */}
      <Modal
        title="新建分支"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateBranch}
        >
          <Form.Item
            name="name"
            label="分支名称"
            rules={[{ required: true, message: '请输入分支名称' }]}
          >
            <Input placeholder="输入分支名称" />
          </Form.Item>
          <Form.Item
            name="ref"
            label="创建自"
            rules={[{ required: true, message: '请输入创建来源' }]}
          >
            <Input placeholder="输入创建来源（分支名或提交SHA）" />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 8 }}
              loading={createBranchLoading}
            >
              创建
            </Button>
            <Button onClick={() => setCreateModalVisible(false)}>取消</Button>
          </Form.Item>
        </Form>
      </Modal>

      <PageContainer
        header={{
          title:'',
          breadcrumb:{}
        }}
        style={{
          height: 'calc(100vh - 48px)',
          overflow: 'hidden',
        }}
      >
        <div
          style={{
            height: 'calc(100vh - 130px)', // 减去头部高度和PageContainer的padding
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            borderRadius: '4px'
          }}
        >
          {/* 面包屑导航 - 固定在顶部 */}
          <div style={{
            flexShrink: 0, // 防止被压缩
            padding: '0 24px',
            marginBottom: '16px'
          }}>
            <RepoBreadcrumb
              customItems={[
                {
                  path: '/tools',
                  breadcrumbName: '工具套件',
                },
                {
                  path: '/tools/repo',
                  breadcrumbName: '代码',
                },
                {
                  path: '/tools/repo/branch',
                  breadcrumbName: '分支管理',
                },
              ]}
            />
          </div>

          {/* 标签页和按钮组 - 固定在顶部 */}
        <div style={{
          flexShrink: 0, // 防止被压缩
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 24px',
          height: '56px', // 固定高度
          borderBottom: '1px solid #f0f0f0'
        }}>
          {/* 左侧标签页 */}
          <div style={{ display: 'flex', flex: 1 }}>
            <Tabs
              activeKey={activeTab}
              onChange={(key) => setActiveTab(key)}
              style={{ width: '100%' }}
            >
              <TabPane key="tab1" tab="概览" />
              <TabPane key="tab2" tab="活跃" />
              <TabPane key="tab3" tab="非活跃" />
              <TabPane key="tab4" tab="全部" />
            </Tabs>
          </div>

          {/* 右侧搜索和按钮 */}
          <div>
            <Space size={12}>
              <Input
                placeholder="搜索分支"
                prefix={<SearchOutlined />}
                style={{ width: 200 }}
                onChange={(e) => handleSearch(e.target.value)}
                allowClear
              />
              <Button
                icon={<DeleteOutlined />}
                danger
                onClick={() => setDeleteMergedModalVisible(true)}
              >
                删除已合并分支
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                style={{background:'#007bff'}}
                onClick={() => setCreateModalVisible(true)}
              >
                新建分支
              </Button>
            </Space>
          </div>
        </div>

        {/* 内容区域 - 可滚动 */}
        <div
          style={{
            flex: 1, // 占据剩余空间
            overflow: 'auto',
            padding: '16px 24px 8px 24px', // 增加底部padding
            scrollPaddingBottom: '8px', // 滚动时底部留白
          }}
        >
          {activeTab === 'tab1' && (
            <>
              {/* 只有当活跃分支数量大于0时才显示活跃分支部分 */}
              {activeBranches.length > 0 && (
                <>
                  <h3>活跃分支</h3>
                  <Divider style={{margin:0}}/>
                  <List
                    size="large"
                    rowKey="id"
                    dataSource={activePreviewBranches}
                    renderItem={(item) => (
                      <BranchList
                        branches={[item]}
                        onCopy={copyBranchName}
                        onDelete={showDeleteConfirm}
                        onNavigate={navigateToBranchFiles}
                        onCompare={handleCompare}
                        onMergeRequest={handleMergeRequest}
                      />
                    )}
                  />
                  <Divider style={{margin:0}}/>
                  <div style={{ marginTop: '12px' }}>
                    <a onClick={() => setActiveTab('tab2')} style={{ cursor: 'pointer' }}>
                      查看更多活跃分支
                    </a>
                  </div>
                  <Divider/>
                </>
              )}

              {/* 只有当非活跃分支数量大于0时才显示非活跃分支部分 */}
              {inactiveBranches.length > 0 && (
                <>
                  <h3>非活跃分支</h3>
                  <Divider style={{margin:0}}/>
                  <List
                    size="large"
                    rowKey="name"
                    dataSource={inactivePreviewBranches}
                    renderItem={(item) => (
                      <BranchList
                        branches={[item]}
                        onCopy={copyBranchName}
                        onDelete={showDeleteConfirm}
                        onNavigate={navigateToBranchFiles}
                        onCompare={handleCompare}
                        onMergeRequest={handleMergeRequest}
                      />
                    )}
                  />
                  <Divider style={{margin:0}}/>
                  <div style={{ margin: '12px 0' }}>
                    <a onClick={() => setActiveTab('tab3')} style={{ cursor: 'pointer' }}>
                      查看更多非活跃分支
                    </a>
                  </div>
                </>
              )}
            </>
          )}

          {activeTab === 'tab2' && (
            <List
              size="large"
              rowKey="name"
              dataSource={activeBranches}
              renderItem={(item) => (
                <BranchList
                  branches={[item]}
                  onCopy={copyBranchName}
                  onDelete={showDeleteConfirm}
                  onNavigate={navigateToBranchFiles}
                  onCompare={handleCompare}
                  onMergeRequest={handleMergeRequest}
                />
              )}
            />
          )}

          {activeTab === 'tab3' && (
            <List
              size="large"
              rowKey="name"
              dataSource={inactiveBranches}
              renderItem={(item) => (
                <BranchList
                  branches={[item]}
                  onCopy={copyBranchName}
                  onDelete={showDeleteConfirm}
                  onNavigate={navigateToBranchFiles}
                  onCompare={handleCompare}
                  onMergeRequest={handleMergeRequest}
                />
              )}
            />
          )}

          {activeTab === 'tab4' && (
            <List
              size="large"
              rowKey="name"
              dataSource={filteredBranch.map(transformBranchData)}
              renderItem={(item) => (
                <BranchList
                  branches={[item]}
                  onCopy={copyBranchName}
                  onDelete={showDeleteConfirm}
                  onNavigate={navigateToBranchFiles}
                  onCompare={handleCompare}
                  onMergeRequest={handleMergeRequest}
                />
              )}
            />
          )}
        </div>
      </div>
    </PageContainer>
    </>
  );
};
