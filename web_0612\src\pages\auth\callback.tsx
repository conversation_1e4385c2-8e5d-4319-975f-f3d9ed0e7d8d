import React, { useEffect, useState } from 'react';
import { Spin, Alert } from 'antd';
import { useModel } from '@umijs/max';

const AuthCallback: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const { initialState, setInitialState } = useModel('@@initialState');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('=== 处理Keycloak回调开始 ===');
        console.log('当前URL:', window.location.href);
        console.log('search参数:', window.location.search);
        console.log('hash参数:', window.location.hash);

        // 解析回调参数 - 支持多种URL格式
        let code, state, sessionState, error, errorDescription;

        // 方法1：检查URL的search部分（nginx重定向后的标准格式）
        const queryParams = new URLSearchParams(window.location.search);
        code = queryParams.get('code');
        state = queryParams.get('state');
        sessionState = queryParams.get('session_state');
        error = queryParams.get('error');
        errorDescription = queryParams.get('error_description');

        console.log('从search部分解析:', {
          code: code ? `存在(${code.substring(0, 10)}...)` : '不存在',
          state: state ? `存在(${state})` : '不存在',
          sessionState: sessionState ? '存在' : '不存在',
          error: error || '无'
        });

        // 方法2：如果search部分没有参数，检查hash部分
        if (!code && !error) {
          const hash = window.location.hash;
          console.log('检查hash部分:', hash);

          if (hash.includes('?')) {
            // 格式：#/auth/callback?code=xxx&state=yyy
            const hashQueryPart = hash.split('?')[1];
            if (hashQueryPart) {
              const hashParams = new URLSearchParams(hashQueryPart);
              code = hashParams.get('code');
              state = hashParams.get('state');
              sessionState = hashParams.get('session_state');
              error = hashParams.get('error');
              errorDescription = hashParams.get('error_description');
              console.log('从hash查询部分解析:', {
                code: code ? `存在(${code.substring(0, 10)}...)` : '不存在',
                state: state ? `存在(${state})` : '不存在',
                sessionState: sessionState ? '存在' : '不存在',
                error: error || '无'
              });
            }
          }
        }

        // 方法3：使用正则表达式从完整URL中提取参数（兜底方案）
        if (!code && !error) {
          const fullUrl = window.location.href;
          console.log('使用正则表达式解析完整URL:', fullUrl);

          const codeMatch = fullUrl.match(/[?&]code=([^&#]+)/);
          const stateMatch = fullUrl.match(/[?&]state=([^&#]+)/);
          const sessionMatch = fullUrl.match(/[?&]session_state=([^&#]+)/);
          const errorMatch = fullUrl.match(/[?&]error=([^&#]+)/);
          const errorDescMatch = fullUrl.match(/[?&]error_description=([^&#]+)/);

          if (codeMatch) {
            code = decodeURIComponent(codeMatch[1]);
            state = stateMatch ? decodeURIComponent(stateMatch[1]) : null;
            sessionState = sessionMatch ? decodeURIComponent(sessionMatch[1]) : null;
            error = errorMatch ? decodeURIComponent(errorMatch[1]) : null;
            errorDescription = errorDescMatch ? decodeURIComponent(errorDescMatch[1]) : null;
            console.log('从URL正则匹配解析:', {
              code: code ? `存在(${code.substring(0, 10)}...)` : '不存在',
              state: state ? `存在(${state})` : '不存在',
              sessionState: sessionState ? '存在' : '不存在',
              error: error || '无'
            });
          }
        }

        // 最终参数汇总
        console.log('=== 最终解析的回调参数 ===');
        console.log('code:', code ? `存在(${code.substring(0, 10)}...)` : '❌ 不存在');
        console.log('state:', state ? `存在(${state})` : '❌ 不存在');
        console.log('session_state:', sessionState ? `存在(${sessionState.substring(0, 10)}...)` : '⚠️ 不存在');
        console.log('error:', error || '✅ 无错误');
        console.log('error_description:', errorDescription || '无');

        // 验证保存的状态参数
        const savedState = sessionStorage.getItem('auth_state');
        const savedNonce = sessionStorage.getItem('auth_nonce');
        const savedCodeVerifier = sessionStorage.getItem('pkce_code_verifier');

        console.log('=== 保存的认证参数 ===');
        console.log('saved_state:', savedState ? `存在(${savedState})` : '❌ 不存在');
        console.log('saved_nonce:', savedNonce ? `存在(${savedNonce})` : '⚠️ 不存在');
        console.log('saved_code_verifier:', savedCodeVerifier ? `存在(${savedCodeVerifier.substring(0, 10)}...)` : '❌ 不存在');

        // 状态验证
        if (state && savedState) {
          if (state === savedState) {
            console.log('✅ 状态验证通过');
          } else {
            console.log('❌ 状态验证失败');
            console.log('  接收到的state:', state);
            console.log('  保存的state:', savedState);
            setError('状态验证失败，可能存在安全风险，请重新登录');
            setTimeout(() => {
              window.location.replace('/#/user/login');
            }, 5000);
            return;
          }
        } else if (state || savedState) {
          console.log('⚠️ 状态参数不完整，但继续处理');
        }

        if (error) {
          console.log('❌ 检测到Keycloak错误:', error, errorDescription);
          setError(`登录失败: ${error} - ${errorDescription || '请重新尝试'}`);
          setTimeout(() => {
            window.location.replace('/#/user/login');
          }, 10000);
          return;
        }

        if (code) {
          console.log('✅ 检测到授权码，开始token交换流程');

          try {
            // 确定正确的重定向URI
            const frontendUrl = window.location.origin;
            const isDevelopment = frontendUrl.includes('localhost') || frontendUrl.includes('127.0.0.1') || frontendUrl.includes(':8088');

            let redirectUri;
            if (isDevelopment) {
              // 开发环境：使用hash路由
              redirectUri = `${frontendUrl}/#/auth/callback`;
            } else {
              // 生产环境：使用nginx重定向
              redirectUri = `${frontendUrl}/auth/callback`;
            }

            console.log('使用的redirectUri:', redirectUri);

            // 获取PKCE code verifier
            console.log('=== 获取PKCE code verifier ===');
            let codeVerifier = savedCodeVerifier;

            if (codeVerifier) {
              console.log('✅ 从sessionStorage获取到PKCE code verifier');
            } else {
              console.log('⚠️ sessionStorage中没有PKCE code verifier，尝试从Keycloak JS客户端获取');

              // 如果没有找到，尝试从Keycloak JS客户端的localStorage获取
              if (state) {
                const possibleKeys = [
                  `kc-callback-${state}`,
                  `keycloak-callback-${state}`,
                  `kc-state-${state}`,
                  state
                ];

                for (const key of possibleKeys) {
                  const keycloakCallbackData = localStorage.getItem(key);
                  if (keycloakCallbackData) {
                    try {
                      const callbackData = JSON.parse(keycloakCallbackData);
                      if (callbackData.pkceCodeVerifier) {
                        codeVerifier = callbackData.pkceCodeVerifier;
                        console.log(`✅ 从Keycloak JS客户端获取到PKCE code verifier (key: ${key})`);
                        // 清除Keycloak的回调数据
                        localStorage.removeItem(key);
                        break;
                      }
                    } catch (e) {
                      console.warn(`解析Keycloak回调数据失败 (key: ${key}):`, e);
                    }
                  }
                }
              }

              // 如果还是没有找到，打印调试信息
              if (!codeVerifier) {
                console.log('❌ 仍然没有找到PKCE code verifier，打印所有相关localStorage keys:');
                for (let i = 0; i < localStorage.length; i++) {
                  const key = localStorage.key(i);
                  if (key && (key.includes('kc-') || key.includes('keycloak') || key.includes(state || ''))) {
                    console.log(`  ${key}:`, localStorage.getItem(key));
                  }
                }
                throw new Error('缺少PKCE code verifier，请重新登录');
              }
            }

            console.log('=== 发送token交换请求到后端 ===');
            console.log('请求参数:', {
              code: code ? `存在(${code.substring(0, 10)}...)` : '不存在',
              state: state || '无',
              redirectUri,
              codeVerifier: codeVerifier ? `存在(${codeVerifier.substring(0, 10)}...)` : '不存在'
            });

            const response = await fetch('/api/auth/callback', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                code: code,
                state: state,
                redirectUri: redirectUri,
                codeVerifier: codeVerifier
              })
            });

            console.log('后端响应状态:', response.status, response.statusText);

            if (!response.ok) {
              const errorText = await response.text();
              console.error('后端响应错误:', errorText);
              throw new Error(`后端请求失败: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            console.log('后端响应结果:', {
              success: result.success,
              hasToken: !!result.token,
              hasUser: !!result.user,
              hasRefreshToken: !!result.refreshToken,
              message: result.message
            });

            if (result.success && result.token) {
              console.log('✅ 后端认证成功，保存认证信息');

              // 保存token和用户信息到localStorage
              localStorage.setItem('keycloak_token', result.token);
              localStorage.setItem('keycloak_user', JSON.stringify(result.user));

              // 如果有refresh token，也保存
              if (result.refreshToken) {
                localStorage.setItem('keycloak_refresh_token', result.refreshToken);
              }

              console.log('Token已保存到localStorage');
              console.log('用户信息:', result.user);

              // 更新全局用户状态
              const currentUser = {
                userid: result.user.id,
                name: result.user.name,
                username: result.user.username,
                email: result.user.email,
                avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(result.user.name || result.user.username)}&background=1890ff&color=fff`,
                sys_access: result.user.sys_access || 'guest',
                roles: result.user.roles || [],
                access: result.user.access || ''
              };

              // 更新initialState
              if (setInitialState) {
                await setInitialState((prevState: any) => ({
                  ...prevState,
                  currentUser,
                }));
                console.log('✅ 用户状态已更新');
              }

              // 清除认证相关的临时数据
              sessionStorage.removeItem('pkce_code_verifier');
              sessionStorage.removeItem('auth_state');
              sessionStorage.removeItem('auth_nonce');

              // 获取重定向路径
              const redirectPath = sessionStorage.getItem('auth_redirect_path') || '/Console/projects';
              sessionStorage.removeItem('auth_redirect_path');

              console.log('✅ 认证流程完成，准备跳转到:', redirectPath);

              // 延迟跳转，确保状态更新完成
              setTimeout(() => {
                console.log('🚀 执行跳转...');
                window.location.replace('/#' + redirectPath);
              }, 1000);

            } else {
              console.error('❌ 后端认证失败:', result);
              throw new Error(result.message || '后端认证失败');
            }
          } catch (error: any) {
            console.error('❌ 后端认证处理失败:', error);
            setError(`认证处理失败: ${error.message}`);
            setTimeout(() => {
              window.location.replace('/#/user/login');
            }, 10000);
          }
        } else {
          // 如果没有授权码，重定向到登录页面
          console.log('❌ 没有授权码，重定向到登录页面');
          setError('无效的回调访问，请重新登录');
          setTimeout(() => {
            window.location.replace('/#/user/login');
          }, 10000);
        }
      } catch (error: any) {
        console.error('❌ 回调处理失败:', error);
        setError(`回调处理失败: ${error.message || '未知错误'}`);
        setTimeout(() => {
          window.location.replace('/#/user/login');
        }, 10000);
      }
    };

    handleCallback();
  }, []);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      padding: '20px',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white'
    }}>
      {error ? (
        <Alert
          message="认证错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <>
          <Spin size="large" style={{ color: 'white' }} />
          <div style={{
            marginTop: 24,
            fontSize: '18px',
            fontWeight: 500,
            textAlign: 'center'
          }}>
            登录成功！正在跳转...
          </div>
          <div style={{
            marginTop: 8,
            fontSize: '14px',
            opacity: 0.8,
            textAlign: 'center'
          }}>
            请稍候，即将进入系统
          </div>
        </>
      )}
    </div>
  );
};

export default AuthCallback;
