const express = require("express");
const { getKeycloak } = require("../config/keycloak");
const { asyncHandler } = require("../middleware/errorHandler");
const { registerUser } = require("../controllers/authController");

// 确保fetch可用（Node.js 18+内置，较老版本需要polyfill）
if (typeof fetch === 'undefined') {
  global.fetch = require('node-fetch');
}

const router = express.Router();

// 用户注册
router.post("/register", asyncHandler(registerUser));

// Keycloak登录重定向
router.get(
  "/login",
  asyncHandler(async (req, res) => {
    const keycloak = getKeycloak();
    const loginUrl = keycloak.loginUrl(
      req.session.id,
      req.query.redirect || process.env.FRONTEND_URL
    );

    res.json({
      success: true,
      loginUrl,
      message: "请重定向到Keycloak登录页面",
    });
  })
);

// 简化的登出端点
router.post(
  "/logout",
  asyncHandler(async (req, res) => {
    try {
      console.log("=== 处理登出请求 ===");
      console.log("请求体:", req.body);
      console.log("请求头:", req.headers.authorization ? "存在Authorization" : "无Authorization");

      // 获取token（从Authorization header或请求体）
      const token =
        req.headers.authorization?.replace("Bearer ", "") || req.body.token;

      // 清除session
      if (req.session) {
        req.session.destroy((err) => {
          if (err) {
            console.error("Session销毁失败:", err);
          } else {
            console.log("Session已销毁");
          }
        });
      }

      // 构建Keycloak登出URL
      const redirectUri =
        req.body.redirectUri ||
        req.query.redirect ||
        `${process.env.FRONTEND_URL}/#/user/login`;

      // 手动构建登出URL，避免使用Keycloak Connect的logoutUrl方法
      const logoutUrl = `${process.env.KEYCLOAK_URL}/realms/${process.env.KEYCLOAK_REALM}/protocol/openid-connect/logout?redirect_uri=${encodeURIComponent(redirectUri)}`;

      console.log("生成的登出URL:", logoutUrl);

      res.json({
        success: true,
        logoutUrl,
        message: "登出成功，请重定向到Keycloak登出页面",
      });
    } catch (error) {
      console.error("登出处理失败:", error);

      // 即使出错也返回基本的登出URL
      const redirectUri = `${process.env.FRONTEND_URL}/#/user/login`;
      const logoutUrl = `${process.env.KEYCLOAK_URL}/realms/${process.env.KEYCLOAK_REALM}/protocol/openid-connect/logout?redirect_uri=${encodeURIComponent(redirectUri)}`;

      res.json({
        success: true,
        logoutUrl,
        message: "登出成功",
      });
    }
  })
);

// 获取Keycloak配置信息（用于前端）
router.get("/config", (req, res) => {
  res.json({
    success: true,
    data: {
      url: process.env.KEYCLOAK_URL,
      realm: process.env.KEYCLOAK_REALM,
      clientId: process.env.KEYCLOAK_CLIENT_ID,
    },
  });
});

// Token验证端点
router.post(
  "/verify",
  asyncHandler(async (req, res) => {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: "缺少token参数",
      });
    }

    try {
      const keycloak = getKeycloak();
      const grant = await keycloak.grantManager.createGrant({
        access_token: token,
      });

      if (grant && grant.access_token) {
        res.json({
          success: true,
          valid: true,
          message: "Token有效",
        });
      } else {
        res.status(401).json({
          success: false,
          valid: false,
          message: "Token无效",
        });
      }
    } catch (error) {
      console.error("Token验证失败:", error);
      res.status(401).json({
        success: false,
        valid: false,
        message: "Token验证失败",
      });
    }
  })
);

// 获取当前用户信息
router.get("/me", asyncHandler(async (req, res) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({
        success: false,
        message: "未提供访问令牌",
      });
    }

    const token = authHeader.substring(7);

    // 验证并解析JWT token
    const jwt = require("jsonwebtoken");
    const jwksClient = require("jwks-rsa");

    const client = jwksClient({
      jwksUri: `${process.env.KEYCLOAK_URL}/realms/${process.env.KEYCLOAK_REALM}/protocol/openid-connect/certs`,
      cache: true,
      cacheMaxEntries: 5,
      cacheMaxAge: 600000,
    });

    const getKey = (header, callback) => {
      client.getSigningKey(header.kid, (err, key) => {
        if (err) {
          return callback(err);
        }
        const signingKey = key.getPublicKey();
        callback(null, signingKey);
      });
    };

    // 验证token - 移除audience验证以避免兼容性问题
    const decoded = await new Promise((resolve, reject) => {
      jwt.verify(
        token,
        getKey,
        {
          issuer: `${process.env.KEYCLOAK_URL}/realms/${process.env.KEYCLOAK_REALM}`,
          algorithms: ["RS256"],
          // 暂时移除audience验证，因为不同的Keycloak配置可能有不同的audience值
          // audience: process.env.KEYCLOAK_CLIENT_ID,
        },
        (err, decoded) => {
          if (err) {
            reject(err);
          } else {
            resolve(decoded);
          }
        }
      );
    });

    // 构建用户信息响应
    const userInfo = {
      userid: decoded.sub,
      name: decoded.name || decoded.preferred_username,
      username: decoded.preferred_username,
      email: decoded.email,
      avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
      access: decoded.realm_access?.roles?.join(',') || '',
      roles: decoded.realm_access?.roles || [],
      clientRoles: decoded.resource_access?.[process.env.KEYCLOAK_CLIENT_ID]?.roles || [],
      // 根据角色确定系统访问级别
      sys_access: decoded.realm_access?.roles?.includes('admin') ? 'admin' :
                  decoded.realm_access?.roles?.includes('owner') ? 'owner' : 'guest'
    };

    res.json({
      success: true,
      data: userInfo
    });

  } catch (error) {
    console.error("获取用户信息失败:", error);
    res.status(401).json({
      success: false,
      message: "无效的访问令牌",
      error: error.message
    });
  }
}));

// Keycloak回调处理端点
router.get(
  "/callback",
  asyncHandler(async (req, res) => {
    try {
      console.log("=== 后端处理Keycloak回调 ===");
      console.log("查询参数:", req.query);

      const { code, redirect } = req.query;

      if (!code) {
        console.error("回调中缺少授权码");
        return res.redirect(
          `${process.env.FRONTEND_URL}/user/login?error=missing_code`
        );
      }

      // 获取重定向路径，默认为主页
      const redirectPath = redirect || "/Console/projects";
      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:8088";

      console.log("准备重定向到:", `${frontendUrl}${redirectPath}`);

      // 重定向到前端，nginx会处理hash路由重定向
      const targetUrl = `${frontendUrl}/auth/callback?code=${encodeURIComponent(
        code
      )}&state=${encodeURIComponent(req.query.state || '')}&session_state=${encodeURIComponent(req.query.session_state || '')}&redirect=${encodeURIComponent(redirectPath)}`;

      console.log("重定向URL:", targetUrl);
      res.redirect(targetUrl);
    } catch (error) {
      console.error("后端回调处理失败:", error);
      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:8088";
      res.redirect(`${frontendUrl}/user/login?error=callback_failed`);
    }
  })
);

// POST回调处理端点 - 处理前端发送的授权码
router.post(
  "/callback",
  asyncHandler(async (req, res) => {
    try {
      console.log("=== 后端处理前端回调请求 ===");
      console.log("请求体:", req.body);

      const { code, state, redirectUri, codeVerifier } = req.body;

      if (!code) {
        return res.status(400).json({
          success: false,
          message: "缺少授权码",
        });
      }

      if (!codeVerifier) {
        return res.status(400).json({
          success: false,
          message: "缺少PKCE code verifier",
        });
      }

      // 使用授权码交换token
      const tokenUrl = `${process.env.KEYCLOAK_URL}/realms/${process.env.KEYCLOAK_REALM}/protocol/openid-connect/token`;
      const tokenParams = {
        grant_type: "authorization_code",
        client_id: process.env.KEYCLOAK_CLIENT_ID,
        client_secret: process.env.KEYCLOAK_CLIENT_SECRET,
        code: code,
        redirect_uri: redirectUri,
        code_verifier: codeVerifier,
      };

      console.log("Token交换请求:");
      console.log("URL:", tokenUrl);
      console.log("参数:", { ...tokenParams, code: "***", client_secret: "***" }); // 隐藏敏感信息

      const tokenResponse = await fetch(tokenUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams(tokenParams),
      });

      if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text();
        console.error("Token交换失败:");
        console.error("状态码:", tokenResponse.status);
        console.error("状态文本:", tokenResponse.statusText);
        console.error("响应内容:", errorText);

        let errorMessage = "Token交换失败";
        try {
          const errorJson = JSON.parse(errorText);
          errorMessage = errorJson.error_description || errorJson.error || errorMessage;
        } catch (e) {
          // 如果不是JSON格式，使用原始错误文本
          errorMessage = errorText || errorMessage;
        }

        return res.status(400).json({
          success: false,
          message: errorMessage,
          error: errorText,
          statusCode: tokenResponse.status,
        });
      }

      const tokenData = await tokenResponse.json();
      // console.log("Token交换成功");
      // console.log("Token数据:", {
      //   access_token: tokenData.access_token ? "存在" : "不存在",
      //   refresh_token: tokenData.refresh_token ? "存在" : "不存在",
      //   expires_in: tokenData.expires_in,
      //   token_type: tokenData.token_type,
      // });

      if (!tokenData.access_token) {
        throw new Error("未收到访问令牌");
      }

      // 解析用户信息
      const jwt = require("jsonwebtoken");
      const decoded = jwt.decode(tokenData.access_token);

      if (!decoded) {
        throw new Error("无法解析访问令牌");
      }

      // console.log("解析的用户信息:", {
      //   sub: decoded.sub,
      //   preferred_username: decoded.preferred_username,
      //   email: decoded.email,
      //   name: decoded.name,
      //   realm_access: decoded.realm_access,
      // });

      const userInfo = {
        id: decoded.sub,
        username: decoded.preferred_username,
        email: decoded.email,
        name: decoded.name,
        roles: decoded.realm_access?.roles || [],
        // 添加系统访问级别
        sys_access: decoded.realm_access?.roles?.includes('admin') ? 'admin' :
                   decoded.realm_access?.roles?.includes('owner') ? 'owner' : 'guest',
        // 添加访问权限字符串
        access: decoded.realm_access?.roles?.join(',') || '',
        // 添加客户端角色
        clientRoles: decoded.resource_access?.[process.env.KEYCLOAK_CLIENT_ID]?.roles || []
      };

      res.json({
        success: true,
        token: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        user: userInfo,
        expiresIn: tokenData.expires_in,
      });
    } catch (error) {
      console.error("后端回调处理失败:", error);
      res.status(500).json({
        success: false,
        message: "服务器内部错误",
        error: error.message,
      });
    }
  })
);

module.exports = router;
