import { PageContainer } from '@ant-design/pro-components';
import { Button, Input, List, Space, Typography, Dropdown, message, Card, Tooltip, Spin, Avatar } from 'antd';
import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { DownOutlined, SearchOutlined, DownloadOutlined, EditOutlined, HistoryOutlined, DeleteOutlined, TagOutlined } from '@ant-design/icons';
import { getCurrentTheme } from './utils';
import { calculateTimeDiff } from './utils/dateUtils';
import { listTags, listProjects } from '@/services/ant-design-pro/gitlab';
import RepoBreadcrumb from '@/components/RepoBreadcrumb';
import { Divider } from 'rc-menu';

const { Text, Paragraph } = Typography;

// 默认标签数据
const defaultTags: API.TagInfo[] = [];

const TagsPage: React.FC = () => {
  const location = useLocation();
  // const navigate = useNavigate();
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>(getCurrentTheme());
  const [tags, setTags] = useState<API.TagInfo[]>(defaultTags);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTags, setFilteredTags] = useState<API.TagInfo[]>(defaultTags);
  const [loading, setLoading] = useState(false);

  // 项目 ID
  const [projectId, setProjectId] = useState<number | undefined>(undefined);

  // 获取项目 ID
  useEffect(() => {
    const fetchProjectId = async () => {
      // 检查location是否携带proId
      if (location.state && location.state.proId) {
        setProjectId(location.state.proId);
      } else {
        // 如果没有，从最近更新的项目中获取
        try {
          const projects = await listProjects();
          if (projects && projects.length > 0) {
            // 按最后活动时间排序（降序）
            const sortedProjects = [...projects].sort((a, b) => {
              const dateA = new Date(a.last_activity_at || 0);
              const dateB = new Date(b.last_activity_at || 0);
              return dateB.getTime() - dateA.getTime();
            });

            // 获取最近更新的项目
            const latestProject = sortedProjects[0];
            setProjectId(latestProject.id);
          }
        } catch (error) {
          console.error('Failed to fetch project information:', error);
          message.error('获取项目信息失败');
        }
      }
    };

    fetchProjectId();
  }, [location.state]);

  // 获取标签数据
  useEffect(() => {
    if (projectId) {
      setLoading(true);
      listTags({ id: projectId })
        .then(response => {
          if (Array.isArray(response)) {
            setTags(response);
            setFilteredTags(response);
          }
        })
        .catch(error => {
          console.error('获取标签列表失败:', error);
          message.error('获取标签列表失败');
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [projectId]);

  // 监听主题变化
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          setCurrentTheme(getCurrentTheme());
        }
      });
    });

    observer.observe(document.body, { attributes: true });

    return () => {
      observer.disconnect();
    };
  }, []);

  // 搜索标签
  useEffect(() => {
    if (searchTerm) {
      const filtered = tags.filter(tag =>
        tag.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredTags(filtered);
    } else {
      setFilteredTags(tags);
    }
  }, [searchTerm, tags]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  // 处理下载
  const handleDownload = (tagName: string) => {
    message.success(`开始下载标签 ${tagName}`);
  };

  return (
    <>
      <RepoBreadcrumb
        customItems={[
          {
            path: '/tools',
            breadcrumbName: '工具套件',
          },
          {
            path: '/tools/repo',
            breadcrumbName: '代码',
          },
          {
            path: '/tools/repo/tags',
            breadcrumbName: '标签',
          },
        ]}
      />
     
      <div>
        <Paragraph style={{ marginBottom: '24px' }}>
          标签使您能够标记特定历史中的特定点标记为重要点
        </Paragraph>

        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
          <Input.Search
            placeholder="根据标签名称筛选"
            onSearch={handleSearch}
            style={{ width: '85%', marginRight: '8px', borderRadius: '4px' }}
            enterButton={<SearchOutlined />}
          />
          <Dropdown
            menu={{
              items: [
                {
                  key: '1',
                  label: '名称',
                },
                {
                  key: '2',
                  label: '最早更新',
                },
                {
                  key: '3',
                  label: (
                    <Space>
                      <span style={{ color: '#1677ff' }}>✓</span>
                      <span>更新日期</span>
                    </Space>
                  ),
                },
                {
                  key: '4',
                  label: '最新版本',
                },
                {
                  key: '5',
                  label: '最旧版本',
                },
              ],
            }}
            trigger={['click']}
            placement="bottomRight"
          >
            <Button style={{  width:'15%',borderRadius: '4px', height: '32px', display: 'flex', alignItems: 'center', border: '1px solid #d9d9d9' }}>
              更新日期 <DownOutlined />
            </Button>
          </Dropdown>
        </div>

        <Spin spinning={loading}>
          <List
            itemLayout="vertical"
            dataSource={filteredTags}
            renderItem={(tag) => (
            <Card
              key={tag.name}
              style={{
                marginBottom: '16px',
                borderRadius: '2px'
              }}
              styles={{ body: { padding: 0 } }}
            >
              <Space direction="vertical" style={{ width: '100%', padding: '16px 24px' }}>
                <Space>
                  <TagOutlined />

                  <Text strong style={{ fontSize: '16px' }}>{tag.name}</Text>

                </Space>
                {tag.commit?.author_name && (
                  <Space>
                    <Avatar size="small" style={{ backgroundColor: '#87d068' }}>
                      {tag.commit?.author_name.charAt(0).toUpperCase()}
                    </Avatar>
                    <Text strong>{tag.commit?.author_name}</Text>
                  </Space>
                )}

                <Space>
                  <Text code>{tag.commit?.short_id}</Text>
                  <Text type="secondary">{tag.commit?.title}</Text>
                  <Text type="secondary">· {calculateTimeDiff(tag.commit?.committed_date || '')}</Text>
                </Space>

                {tag.message && (
                  <Card
                    style={{
                      backgroundColor: currentTheme === 'light' ? '#fff' : '#2c2c2c',
                      marginTop: '8px',
                      borderRadius: '2px'
                    }}
                    styles={{ body: { padding: '16px' } }}
                  >
                    <Text>{tag.message}</Text>
                  </Card>
                )}
              </Space>

              <Space style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
              }}>
                <Tooltip title="编辑">
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                  />
                </Tooltip>
                <Tooltip title="历史">
                  <Button
                    type="text"
                    icon={<HistoryOutlined />}
                  />
                </Tooltip>
                <Tooltip title="下载">
                  <Button
                    type="text"
                    icon={<DownloadOutlined />}
                    onClick={() => handleDownload(tag.name)}
                  />
                </Tooltip>
                <Tooltip title="删除">
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    danger
                  />
                </Tooltip>
              </Space>
            </Card>
          )}
          />
        </Spin>
      </div>
      </>
  );
};

export default TagsPage;