// Import shared components and utilities
import RepoBreadcrumb from '@/components/RepoBreadcrumb';
import { listBranch, listCommits, listProjects } from '@/services/ant-design-pro/gitlab';
import {
  CopyOutlined,
  DownOutlined,
  FolderOutlined,
  SearchOutlined,
  WifiOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Avatar,
  Button,
  Card,
  Col,
  Dropdown,
  Empty,
  Input,
  List,
  message,
  Row,
  Space,
  Spin,
  Tooltip,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { Divider } from 'rc-menu';
import { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import './commit.less';
import { BranchDropdown } from './components';
import { getCurrentTheme } from './utils';

// Initialize dayjs plugins
dayjs.extend(relativeTime);

export default () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [projectId, setProjectId] = useState<number | undefined>(undefined);
  const [currentBranch, setCurrentBranch] = useState<string>('');
  const [projectName, setProjectName] = useState<string>('');
  const [filteredCommits, setFilteredCommits] = useState<API.CommitInfo[]>([]);
  // Using empty array for branches since we're not fetching them in this component
  const [branches, setBranches] = useState<API.BranchInfo[]>([]);
  const [searchText, setSearchText] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [authorSearchText, setAuthorSearchText] = useState<string>('');
  const [selectedAuthor, setSelectedAuthor] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMoreCommits, setHasMoreCommits] = useState<boolean>(true);
  const [authorDropdownVisible, setAuthorDropdownVisible] = useState<boolean>(false);

  // 获取项目ID，如果location没有携带信息，则从最近更新的项目中获取
  useEffect(() => {
    const fetchProjectId = async () => {
      setLoading(true);

      // 检查location是否携带proId
      if (location.state && location.state.proId) {
        const proId = location.state.proId;
        const defaultBranch = location.state.defaultBranch || 'main';
        const proName = location.state.proName || '';

        setProjectId(proId);
        setCurrentBranch(defaultBranch);
        setProjectName(proName);
      } else {
        // 如果没有，从最近更新的项目中获取
        try {
          const projects = await listProjects();

          if (projects && projects.length > 0) {
            // 按最后活动时间排序（降序）
            const sortedProjects = [...projects].sort((a, b) => {
              const dateA = new Date(a.last_activity_at || 0);
              const dateB = new Date(b.last_activity_at || 0);
              return dateB.getTime() - dateA.getTime();
            });

            // 获取最近更新的项目
            const latestProject = sortedProjects[0];
            const proId = latestProject.id;
            const defaultBranch = latestProject.default_branch || 'main';
            const proName = latestProject.name || '';

            setProjectId(proId);
            setCurrentBranch(defaultBranch);
            setProjectName(proName);
          } else {
            console.warn('没有找到可用的项目');
          }
        } catch (error) {
          console.error('获取项目信息失败:', error);
        }
      }
      setLoading(false);
    };

    fetchProjectId();
  }, [location.state]);

  // Fetch branches when projectId is available
  useEffect(() => {
    if (!projectId) {
      return;
    }

    const fetchBranches = async () => {
      setLoading(true);
      try {
        // Fetch branches
        const branchResponse = await listBranch({ id: projectId });

        if (Array.isArray(branchResponse) && branchResponse.length > 0) {
          setBranches(branchResponse);

          // 如果当前没有选中分支，则使用默认分支
          if (!currentBranch) {
            // 查找默认分支
            const defaultBranch = branchResponse.find((branch) => branch.default);
            if (defaultBranch) {
              setCurrentBranch(defaultBranch.name);
            } else if (branchResponse.length > 0) {
              // 如果没有标记为默认的分支，使用第一个分支
              setCurrentBranch(branchResponse[0].name);
            }
          }
        } else {
          setBranches([]);
        }
      } catch (error) {
        console.error('获取分支失败:', error);
        setBranches([]);
      } finally {
        setLoading(false);
      }
    };

    fetchBranches();
  }, [projectId]);

  // Fetch commits when projectId, currentBranch or selectedAuthor changes
  useEffect(() => {
    if (!projectId || !currentBranch) {
      return;
    }

    console.log(
      `分支或作者变更，重新获取提交记录: ${currentBranch}, 作者: ${selectedAuthor || '所有作者'}`,
    );

    // 重置分页状态
    setCurrentPage(1);
    setHasMoreCommits(true);
    setFilteredCommits([]);

    // 获取提交记录
    fetchCommits(1);
  }, [projectId, currentBranch]);

  // 获取提交记录的函数
  const fetchCommits = async (page: number) => {
    if (!projectId) return;

    setLoading(true);
    try {
      // Fetch commits
      const params = { id: projectId } as any;

      // 如果有当前分支，添加到参数中
      if (currentBranch) {
        params.refname = currentBranch; // 使用 ref_name 参数作为选中的分支
        console.log(
          `调用listCommits API: id=${projectId}, ref_name=${currentBranch}, page=${page}`,
        );
      } else {
        console.log(`调用listCommits API: id=${projectId}, 使用默认分支, page=${page}`);
      }

      // 如果选择了特定作者，添加到参数中
      if (selectedAuthor) {
        params.author = selectedAuthor;
        console.log(`添加作者筛选: author=${selectedAuthor}`);
      }

      // 添加参数以获取更多提交记录
      // params.per_page = 100; // 每页显示100条记录
      // params.page = page; // 指定页码
      // params.all = true; // 获取所有提交记录

      // 调用API获取提交记录
      const commitResponse = await listCommits(params);

      // 检查响应是否为空
      if (!commitResponse) {
        console.warn('提交记录响应为空');
        setFilteredCommits([]);
        return;
      }

      // 检查是否为数组
      if (!Array.isArray(commitResponse)) {
        console.warn('提交记录响应不是数组格式:', typeof commitResponse);

        // 如果是单个提交对象，将其转换为数组
        if (typeof commitResponse === 'object' && commitResponse !== null) {
          const commit = commitResponse as any;
          if (commit.id && commit.short_id) {
            console.log('将单个提交对象转换为数组');
            const singleCommitArray = [commit];
            setFilteredCommits(singleCommitArray);
            return;
          }
        }

        // 尝试将非数组响应转换为数组
        try {
          if (typeof commitResponse === 'object' && commitResponse !== null) {
            // 如果是对象，尝试提取数组属性
            const possibleArrays = Object.values(commitResponse).filter((val) =>
              Array.isArray(val),
            );
            if (possibleArrays.length > 0) {
              const extractedArray = possibleArrays[0] as any[];
              console.log(`从响应中提取到 ${extractedArray.length} 个提交`);
              setFilteredCommits(extractedArray);
              return;
            }

            // 如果对象有多个提交属性，尝试将它们合并为一个数组
            const possibleCommits = Object.values(commitResponse).filter(
              (val) => typeof val === 'object' && val !== null && 'id' in val && 'short_id' in val,
            );

            if (possibleCommits.length > 0) {
              console.log(`从响应中提取到 ${possibleCommits.length} 个提交对象`);
              setFilteredCommits(possibleCommits as any[]);
              return;
            }
          }
          // 如果无法提取数组，设置为空数组
          setFilteredCommits([]);
        } catch (error) {
          console.error('处理非数组响应时出错:', error);
          setFilteredCommits([]);
        }
        return;
      }

      // 如果是空数组
      if (commitResponse.length === 0) {
        console.warn('提交记录数组为空');
        setFilteredCommits([]);
        return;
      }

      // 设置提交记录到状态
      setFilteredCommits(commitResponse);
    } catch (error) {
      console.error('获取数据失败:', error);
      // 出错时清空数据，避免显示错误数据
      setBranches([]);
      setFilteredCommits([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理分支选择
  const handleBranchSelect = (key: string) => {
    // 检查projectId是否可用
    if (!projectId) {
      console.warn('无法切换分支: projectId不可用');
      return;
    }

    // 解析分支索引
    const selectedBranchIndex = parseInt(key.replace('branch-', ''), 10);

    // 验证分支索引和分支数组
    if (isNaN(selectedBranchIndex)) {
      console.warn(`无法解析分支索引: key=${key}`);
      return;
    }

    if (!branches || !branches[selectedBranchIndex]) {
      console.warn(
        `分支数组为空或索引无效: index=${selectedBranchIndex}, branches.length=${
          branches?.length || 0
        }`,
      );
      return;
    }

    // 获取选中的分支
    const selectedBranch = branches[selectedBranchIndex];
    const branchName = selectedBranch.name;

    console.log(`选择分支: ${branchName}`);

    // 更新当前分支状态
    setCurrentBranch(branchName);

    // 获取选中分支的提交记录
    // 注意: 我们不需要在这里调用listCommits，因为已经在useEffect中监听了currentBranch的变化
    // 当setCurrentBranch执行后，useEffect会自动触发并获取新分支的提交记录
  };

  // Format commits by date
  // Format commits by date - memoize this function to avoid recalculating on every render
  const formatCommitsByDate = () => {
    // 确保有效的提交数组
    if (!filteredCommits) {
      console.warn('格式化提交记录: filteredCommits为空');
      return {};
    }

    if (!Array.isArray(filteredCommits)) {
      console.warn('格式化提交记录: filteredCommits不是数组', typeof filteredCommits);
      return {};
    }

    if (filteredCommits.length === 0) {
      console.warn('格式化提交记录: filteredCommits数组为空');
      return {};
    }

    try {
      // 使用API的真实提交数据
      const commits = filteredCommits;
      console.log(`格式化 ${commits.length} 条提交记录`);

      // 根据搜索文本过滤提交
      let filteredBySearch;
      try {
        filteredBySearch = commits.filter((commit: any) => {
          // 确保提交对象有效
          if (!commit || typeof commit !== 'object') {
            console.warn('无效的提交对象:', commit);
            return false;
          }

          // 根据搜索文本过滤
          const matchesSearchText =
            !searchText ||
            (commit.title?.toLowerCase() || '').includes(searchText.toLowerCase()) ||
            (commit.author_name?.toLowerCase() || '').includes(searchText.toLowerCase()) ||
            (commit.short_id?.toLowerCase() || '').includes(searchText.toLowerCase());

          // 由于已经在API调用中过滤了作者，这里不需要再次过滤
          // 直接返回搜索文本匹配结果
          return matchesSearchText;
        });

        console.log(`过滤后剩余 ${filteredBySearch.length} 条提交记录`);
      } catch (error) {
        console.error('过滤提交记录时出错:', error);
        filteredBySearch = commits; // 出错时使用原始数组
      }

      // 确保 filteredBySearch 是数组
      if (!Array.isArray(filteredBySearch)) {
        console.warn('过滤后的提交记录不是数组', filteredBySearch);
        return {};
      }

      // 按日期分组提交
      const groupedCommits: Record<string, any[]> = {};

      filteredBySearch.forEach((commit: any) => {
        try {
          // 确保提交对象有效
          if (!commit || !commit.committed_date) {
            console.warn('提交对象缺少committed_date:', commit);
            return; // 跳过此提交
          }

          // 格式化日期如图所示: "17 Apr, 2025"
          const date = dayjs(commit.committed_date).format('D MMM, YYYY');
          if (!groupedCommits[date]) {
            groupedCommits[date] = [];
          }
          groupedCommits[date].push(commit);
        } catch (error) {
          console.error('处理提交记录时出错:', commit, error);
          // 继续处理下一条记录
        }
      });

      console.log(`按日期分组后有 ${Object.keys(groupedCommits).length} 个日期组`);
      return groupedCommits;
    } catch (error) {
      console.error('格式化提交记录时出错:', error);
      return {}; // 出错时返回空对象
    }
  };

  // 获取所有作者
  const getUniqueAuthors = useMemo(() => {
    if (!filteredCommits || !Array.isArray(filteredCommits) || filteredCommits.length === 0) {
      return [];
    }

    // 从提交记录中提取所有不同的作者
    const authors = filteredCommits
      .map((commit: any) => commit.author_name)
      .filter((name: any): name is string => !!name); // 过滤掉空值

    // 去重
    const uniqueAuthors = [...new Set(authors)];
    return uniqueAuthors;
  }, [filteredCommits]);

  // 根据搜索文本过滤作者
  const filteredAuthors = useMemo(() => {
    if (authorSearchText) {
      return getUniqueAuthors.filter((author: string) =>
        author.toLowerCase().includes(authorSearchText.toLowerCase()),
      );
    }
    return getUniqueAuthors;
  }, [getUniqueAuthors, authorSearchText]);

  // 处理作者选择
  const handleAuthorSelect = (author: string) => {
    const newAuthor = author === '任何作者' ? '' : author;
    setSelectedAuthor(newAuthor);
    setAuthorDropdownVisible(false);

    // 重置分页状态
    setCurrentPage(1);
    setHasMoreCommits(true);
    setFilteredCommits([]);

    // 重新获取提交记录
    fetchCommits(1);
  };

  // 监听filteredCommits的变化
  useEffect(() => {
    // console.log(`filteredCommits已更新: ${filteredCommits?.length || 0}条记录`);
  }, [filteredCommits]);

  // 使用useMemo缓存格式化结果，在filteredCommits或searchText变化时重新计算
  // 注意：selectedAuthor不再需要作为依赖项，因为我们在API调用中已经处理了作者过滤
  const groupedCommits = useMemo(() => {
    return formatCommitsByDate();
  }, [filteredCommits, searchText]);
  const [currentTheme] = useState<'light' | 'dark'>(getCurrentTheme());

  return (
    <>
    <RepoBreadcrumb
        customItems={[
          {
            path: '/tools',
            breadcrumbName: '工具套件',
          },
          {
            path: '/tools/repo',
            breadcrumbName: '代码',
          },
          {
            path: '/tools/repo/commits',
            breadcrumbName: '提交管理',
          },
        ]}
      />
      
      <Row justify="space-between" align="middle">
        <Col flex="auto">
          <BranchDropdown
            branches={branches}
            defaultBranch={currentBranch || 'main'}
            onBranchSelect={handleBranchSelect}
          />
          <span style={{ fontSize: 16 }}>{projectName} / </span>
        </Col>
        <Col>
          <Dropdown
            trigger={['click']}
            open={authorDropdownVisible}
            onOpenChange={(visible) => setAuthorDropdownVisible(visible)}
            dropdownRender={() => (
              <div
                style={{
                  width: 300,
                  background: currentTheme === 'light' ? '#fff' : '#1a1c1e',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                  borderRadius: 4,
                  padding: '8px 0',
                }}
              >
                <div
                  style={{
                    padding: '8px 12px',
                    fontWeight: 'bold',
                    borderBottom: '1px solid #f0f0f0',
                  }}
                >
                  按作者搜索
                </div>
                <div style={{ padding: '8px 12px' }}>
                  <Input
                    placeholder="搜索"
                    prefix={<SearchOutlined />}
                    value={authorSearchText}
                    onChange={(e) => setAuthorSearchText(e.target.value)}
                    style={{ width: '100%' }}
                  />
                </div>
                <div style={{ maxHeight: 300, overflow: 'auto' }}>
                  <div
                    style={{
                      padding: '8px 12px',
                      cursor: 'pointer',
                      backgroundColor: selectedAuthor === '' ? '#f0f0f0' : 'transparent',
                      display: 'flex',
                      alignItems: 'center',
                    }}
                    onClick={() => handleAuthorSelect('任何作者')}
                  >
                    {selectedAuthor === '' && (
                      <span style={{ color: '#1890ff', marginRight: 8 }}>✓</span>
                    )}
                    <span>任何作者</span>
                  </div>
                  {filteredAuthors.map((author) => (
                    <div
                      key={author}
                      style={{
                        padding: '8px 12px',
                        cursor: 'pointer',
                        backgroundColor: selectedAuthor === author ? '#f0f0f0' : 'transparent',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                      onClick={() => handleAuthorSelect(author)}
                    >
                      {selectedAuthor === author && (
                        <span style={{ color: '#1890ff', marginRight: 8 }}>✓</span>
                      )}
                      <span>{author}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          >
            <Button onClick={(e) => e.preventDefault()} size="large" style={{ marginRight: 16 }}>
              <Space>
                {selectedAuthor || '作者'}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
          <Button
            onClick={() => {
              navigate('/tools/repo/newMergeRequest', {
                state: {
                  proId: projectId,
                  proName: projectName,
                  sourceBranch: currentBranch,
                  targetBranch: branches.find((branch) => branch.default)?.name || 'main',
                },
              });
            }}
            size="large"
            style={{ background: '#87A9FF', marginRight: 6 }}
          >
            <Space>创建合并请求</Space>
          </Button>

          <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>
            <Input
              placeholder="按消息搜索"
              prefix={<SearchOutlined />}
              style={{
                width: 200,
                height: '40px',
                marginRight: 6,
              }}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </span>
          <Button
            // onClick={handleFileSearch}
            size="large"
            style={{ marginRight: 6 }}
          >
            <Space>
              <WifiOutlined />
            </Space>
          </Button>
        </Col>
      </Row>
      <br></br>
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>加载提交记录中...</div>
        </div>
      ) : Object.keys(groupedCommits).length > 0 ? (
        Object.keys(groupedCommits).map((date) => (
          <div key={date}>
            <Card
              style={{
                fontSize: 16,
                fontWeight: 'normal',
                display: 'block',
                marginTop: 16,
                marginBottom: 8,
                border: 0,
                // background: currentTheme === 'light' ? '#fafafa' : '#2c2c2c',
                // borderBottom:'1px solid #eee'
              }}
              title={
                <span>
                  {date} {groupedCommits[date].length} commit
                  {groupedCommits[date].length > 1 ? 's' : ''}
                </span>
              }
            >
              <List
                size="large"
                // style={{ backgroundColor: currentTheme === 'light' ? '#fff' : '#1a1c1e' }}
                dataSource={groupedCommits[date]}
                renderItem={(item: any) => (
                  <List.Item
                    style={{ padding: '12px 0' }}
                    actions={[
                      <Typography.Text code style={{ padding: '4px 8px' }}>
                        {item.short_id}
                      </Typography.Text>,
                      <Tooltip title="复制提交SHA">
                        <Button
                          type="text"
                          style={{ border: '0' }}
                          onClick={() => {
                            navigator.clipboard
                              .writeText(item.short_id)
                              .then(() => {
                                message.success('提交ID已复制到剪贴板');
                              })
                              .catch((err) => {
                                console.error('复制失败:', err);
                                message.error('复制失败');
                              });
                          }}
                        >
                          <CopyOutlined />
                        </Button>
                      </Tooltip>,
                      <Tooltip title="浏览文件">
                        <Button
                          type="text"
                          style={{ border: '0' }}
                          onClick={() => {
                            navigate('/tools/repo/files', {
                              state: {
                                proId: projectId,
                                defaultBranch: currentBranch,
                                proName: projectName,
                                sha: item.id, // Pass the commit SHA
                              },
                            });
                          }}
                        >
                          <FolderOutlined />
                        </Button>
                      </Tooltip>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar size="large" style={{ backgroundColor: '#87d068' }}>
                          {item.author_name.charAt(0).toUpperCase()}
                        </Avatar>
                      }
                      title={
                        <Typography.Text strong style={{ fontSize: 16 }}>
                          {item.title}
                        </Typography.Text>
                      }
                      description={
                        <Typography.Text type="secondary" style={{ fontSize: 14 }}>
                          {item.author_name} authored {dayjs(item.committed_date).fromNow()}
                        </Typography.Text>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </div>
        ))
      ) : (
        <Empty description="暂无提交记录" style={{ margin: '50px 0' }} />
      )}
    </>
  );
};
